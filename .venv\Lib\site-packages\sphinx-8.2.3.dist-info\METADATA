Metadata-Version: 2.4
Name: Sphinx
Version: 8.2.3
Summary: Python documentation generator
Author-email: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
Requires-Python: >=3.11
Description-Content-Type: text/x-rst
License-Expression: BSD-2-Clause
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: Other Audience
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: System Administrators
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Framework :: Sphinx
Classifier: Framework :: Sphinx :: Domain
Classifier: Framework :: Sphinx :: Extension
Classifier: Framework :: Sphinx :: Theme
Classifier: Topic :: Documentation
Classifier: Topic :: Documentation :: Sphinx
Classifier: Topic :: Education
Classifier: Topic :: Internet :: WWW/HTTP :: Site Management
Classifier: Topic :: Internet :: WWW/HTTP :: Site Management :: Link Checking
Classifier: Topic :: Printing
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Documentation
Classifier: Topic :: Text Editors :: Documentation
Classifier: Topic :: Text Processing
Classifier: Topic :: Text Processing :: General
Classifier: Topic :: Text Processing :: Indexing
Classifier: Topic :: Text Processing :: Markup
Classifier: Topic :: Text Processing :: Markup :: HTML
Classifier: Topic :: Text Processing :: Markup :: LaTeX
Classifier: Topic :: Text Processing :: Markup :: Markdown
Classifier: Topic :: Text Processing :: Markup :: reStructuredText
Classifier: Topic :: Text Processing :: Markup :: XML
Classifier: Topic :: Utilities
Classifier: Typing :: Typed
License-File: LICENSE.rst
Requires-Dist: sphinxcontrib-applehelp>=1.0.7
Requires-Dist: sphinxcontrib-devhelp>=1.0.6
Requires-Dist: sphinxcontrib-htmlhelp>=2.0.6
Requires-Dist: sphinxcontrib-jsmath>=1.0.1
Requires-Dist: sphinxcontrib-qthelp>=1.0.6
Requires-Dist: sphinxcontrib-serializinghtml>=1.1.9
Requires-Dist: Jinja2>=3.1
Requires-Dist: Pygments>=2.17
Requires-Dist: docutils>=0.20,<0.22
Requires-Dist: snowballstemmer>=2.2
Requires-Dist: babel>=2.13
Requires-Dist: alabaster>=0.7.14
Requires-Dist: imagesize>=1.3
Requires-Dist: requests>=2.30.0
Requires-Dist: roman-numerals-py>=1.0.0
Requires-Dist: packaging>=23.0
Requires-Dist: colorama>=0.4.6; sys_platform == 'win32'
Requires-Dist: sphinxcontrib-websupport ; extra == "docs"
Requires-Dist: ruff==0.9.9 ; extra == "lint"
Requires-Dist: mypy==1.15.0 ; extra == "lint"
Requires-Dist: sphinx-lint>=0.9 ; extra == "lint"
Requires-Dist: types-colorama==0.4.15.20240311 ; extra == "lint"
Requires-Dist: types-defusedxml==0.7.0.20240218 ; extra == "lint"
Requires-Dist: types-docutils==0.21.0.20241128 ; extra == "lint"
Requires-Dist: types-Pillow==10.2.0.20240822 ; extra == "lint"
Requires-Dist: types-Pygments==2.19.0.20250219 ; extra == "lint"
Requires-Dist: types-requests==2.32.0.20241016 ; extra == "lint"
Requires-Dist: types-urllib3==1.26.25.14 ; extra == "lint"
Requires-Dist: pyright==1.1.395 ; extra == "lint"
Requires-Dist: pytest>=8.0 ; extra == "lint"
Requires-Dist: pypi-attestations==0.0.21 ; extra == "lint"
Requires-Dist: betterproto==2.0.0b6 ; extra == "lint"
Requires-Dist: pytest>=8.0 ; extra == "test"
Requires-Dist: pytest-xdist[psutil]>=3.4 ; extra == "test"
Requires-Dist: defusedxml>=0.7.1 ; extra == "test"
Requires-Dist: cython>=3.0 ; extra == "test"
Requires-Dist: setuptools>=70.0 ; extra == "test"
Requires-Dist: typing_extensions>=4.9 ; extra == "test"
Project-URL: Changelog, https://www.sphinx-doc.org/en/master/changes.html
Project-URL: Code, https://github.com/sphinx-doc/sphinx
Project-URL: Documentation, https://www.sphinx-doc.org/
Project-URL: Download, https://pypi.org/project/Sphinx/
Project-URL: Homepage, https://www.sphinx-doc.org/
Project-URL: Issue tracker, https://github.com/sphinx-doc/sphinx/issues
Provides-Extra: docs
Provides-Extra: lint
Provides-Extra: test

========
 Sphinx
========

.. image:: https://img.shields.io/pypi/v/sphinx.svg
   :target: https://pypi.org/project/Sphinx/
   :alt: Package on PyPI

.. image:: https://github.com/sphinx-doc/sphinx/actions/workflows/main.yml/badge.svg
   :target: https://github.com/sphinx-doc/sphinx/actions/workflows/main.yml
   :alt: Build Status

.. image:: https://readthedocs.org/projects/sphinx/badge/?version=master
   :target: https://www.sphinx-doc.org/
   :alt: Documentation Status

.. image:: https://img.shields.io/badge/License-BSD%202--Clause-blue.svg
   :target: https://opensource.org/licenses/BSD-2-Clause
   :alt: BSD 2 Clause

**Sphinx makes it easy to create intelligent and beautiful documentation.**

Sphinx uses reStructuredText as its markup language, and many of its strengths
come from the power and straightforwardness of reStructuredText and its parsing
and translating suite, the Docutils.

Features
========

* **Output formats**: HTML, PDF, plain text, EPUB, TeX, manual pages, and more
* **Extensive cross-references**: semantic markup and automatic links
  for functions, classes, glossary terms and similar pieces of information
* **Hierarchical structure**: easy definition of a document tree, with automatic
  links to siblings, parents and children
* **Automatic indices**: general index as well as a module index
* **Code highlighting**: automatic highlighting using the Pygments highlighter
* **Templating**: Flexible HTML output using the Jinja 2 templating engine
* **Extension ecosystem**: Many extensions are available, for example for
  automatic function documentation or working with Jupyter notebooks.
* **Language Support**: Python, C, C++, JavaScript, mathematics, and many other
  languages through extensions.

For more information, refer to `the documentation`_.

Installation
============

The following command installs Sphinx from the `Python Package Index`_. You will
need a working installation of Python and pip.

.. code-block:: shell

   pip install -U sphinx

Contributing
============

We appreciate all contributions! Refer to `the contributors guide`_ for
information.

.. _the documentation: https://www.sphinx-doc.org/
.. _the contributors guide: https://www.sphinx-doc.org/en/master/internals/contributing.html
.. _Python Package Index: https://pypi.org/project/Sphinx/

