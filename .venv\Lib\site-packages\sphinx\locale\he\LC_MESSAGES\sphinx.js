Documentation.addTranslations({
    "locale": "he",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": "",
        "About these documents": "\u05e2\u05dc \u05de\u05e1\u05de\u05db\u05d9\u05dd \u05d0\u05dc\u05d5",
        "Automatically generated list of changes in version %(version)s": "\u05d9\u05e6\u05e8 \u05d0\u05d5\u05d8\u05d5\u05de\u05d8\u05d9\u05ea \u05e8\u05e9\u05d9\u05de\u05d4 \u05e9\u05dc \u05e9\u05d9\u05e0\u05d5\u05d9\u05d9\u05dd \u05d1\u05d2\u05e8\u05e1\u05d4 %(version)s",
        "C API changes": "",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "",
        "Collapse sidebar": "\u05db\u05d5\u05d5\u05e5 \u05e1\u05e8\u05d2\u05dc \u05e6\u05d3",
        "Complete Table of Contents": "\u05ea\u05d5\u05db\u05df \u05e2\u05e0\u05d9\u05d9\u05e0\u05d9\u05dd \u05de\u05dc\u05d0",
        "Contents": "\u05ea\u05d5\u05db\u05df",
        "Copyright": "\u05d6\u05db\u05d5\u05d9\u05d5\u05ea \u05e9\u05de\u05d5\u05e8\u05d5\u05ea",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "\u05d4\u05e8\u05d7\u05d1 \u05e1\u05e8\u05d2\u05dc \u05e6\u05d3",
        "Full index on one page": "\u05d0\u05d9\u05e0\u05d3\u05e7\u05e1 \u05de\u05dc\u05d0 \u05d1\u05e2\u05de\u05d5\u05d3 \u05d0\u05d7\u05d3",
        "General Index": "",
        "Global Module Index": "\u05d0\u05d9\u05e0\u05d3\u05e7\u05e1 \u05de\u05d5\u05d3\u05d5\u05dc\u05d9\u05dd \u05d2\u05dc\u05d5\u05d1\u05dc\u05d9",
        "Go": "\u05dc\u05da",
        "Hide Search Matches": "\u05d4\u05e1\u05ea\u05e8 \u05ea\u05d5\u05e6\u05d0\u05d5\u05ea \u05d7\u05d9\u05e4\u05d5\u05e9",
        "Index": "\u05d0\u05d9\u05e0\u05d3\u05e7\u05e1",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "\u05e2\u05de\u05d5\u05d3\u05d9 \u05d0\u05d9\u05e0\u05d3\u05e7\u05e1 \u05dc\u05e4\u05d9 \u05d0\u05d5\u05ea\u05d9\u05d5\u05ea",
        "Indices and tables:": "",
        "Last updated on %(last_updated)s.": "\u05e2\u05d5\u05d3\u05db\u05df \u05dc\u05d0\u05d7\u05e8\u05d5\u05e0\u05d4 \u05d1 %(last_updated)s.",
        "Library changes": "",
        "Navigation": "\u05e0\u05d9\u05d5\u05d5\u05d8",
        "Next topic": "\u05e0\u05d5\u05e9\u05d0 \u05d4\u05d1\u05d0",
        "Other changes": "\u05e9\u05d9\u05e0\u05d5\u05d9\u05d9\u05dd \u05d0\u05d7\u05e8\u05d9\u05dd",
        "Overview": "\u05e1\u05e7\u05d9\u05e8\u05d4 \u05db\u05dc\u05dc\u05d9\u05ea",
        "Please activate JavaScript to enable the search\n    functionality.": "\u05d0\u05e0\u05d0 \u05d4\u05e4\u05e2\u05dc \u05d2'\u05d0\u05d5\u05d0\u05e1\u05e7\u05e8\u05d9\u05e4\u05d8 \u05e2\"\u05de \u05dc\u05d0\u05e4\u05e9\u05e8 \u05d0\u05ea\n    \u05d4\u05d7\u05d9\u05e4\u05d5\u05e9.",
        "Preparing search...": "",
        "Previous topic": "\u05e0\u05d5\u05e9\u05d0 \u05e7\u05d5\u05d3\u05dd",
        "Quick search": "\u05d7\u05d9\u05e4\u05d5\u05e9 \u05de\u05d4\u05d9\u05e8",
        "Search": "\u05d7\u05d9\u05e4\u05d5\u05e9",
        "Search Page": "\u05d3\u05e3 \u05d7\u05d9\u05e4\u05d5\u05e9",
        "Search Results": "\u05ea\u05d5\u05e6\u05d0\u05d5\u05ea \u05d4\u05d7\u05d9\u05e4\u05d5\u05e9",
        "Search finished, found one page matching the search query.": [
            "",
            "",
            ""
        ],
        "Search within %(docstitle)s": "\u05d7\u05e4\u05e9 \u05d1\u05ea\u05d5\u05da %(docstitle)s",
        "Searching": "",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "\u05d4\u05e6\u05d2 \u05de\u05e7\u05d5\u05e8",
        "Table of Contents": "",
        "This Page": "\u05e2\u05de\u05d5\u05d3 \u05d6\u05d4",
        "Welcome! This is": "",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "",
        "all functions, classes, terms": "\u05db\u05dc \u05d4\u05e4\u05d5\u05e0\u05e7\u05e6\u05d9\u05d5\u05ea, \u05d4\u05de\u05d7\u05dc\u05e7\u05d5\u05ea, \u05d4\u05de\u05d5\u05e9\u05d2\u05d9\u05dd",
        "can be huge": "\u05e2\u05e9\u05d5\u05d9 \u05dc\u05d4\u05d9\u05d5\u05ea \u05e2\u05e6\u05d5\u05dd",
        "last updated": "",
        "lists all sections and subsections": "",
        "next chapter": "\u05e4\u05e8\u05e7 \u05d4\u05d1\u05d0",
        "previous chapter": "\u05e4\u05e8\u05e7 \u05e7\u05d5\u05d3\u05dd",
        "quick access to all modules": "\u05d2\u05d9\u05e9\u05d4 \u05de\u05d4\u05d9\u05e8\u05d4 \u05dc\u05db\u05dc \u05d4\u05de\u05d5\u05d3\u05d5\u05dc\u05d9\u05dd",
        "search": "\u05d7\u05d9\u05e4\u05d5\u05e9",
        "search this documentation": "\u05d7\u05e4\u05e9 \u05d1\u05ea\u05d9\u05e2\u05d5\u05d3 \u05d6\u05d4",
        "the documentation for": ""
    },
    "plural_expr": "(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2"
});