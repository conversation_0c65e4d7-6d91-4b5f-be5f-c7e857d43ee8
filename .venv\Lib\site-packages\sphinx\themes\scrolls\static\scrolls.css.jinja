/*
 * Sphinx stylesheet -- scrolls theme.
 */

body {
    background-color: #222;
    margin: 0;
    padding: 0;
    font-family: 'Georgia', serif;
    font-size: 15px;
    color: #eee;
}

div.footer {
    border-top: 1px solid #111;
    padding: 8px;
    font-size: 11px;
    text-align: center;
    letter-spacing: 0.5px;
}

div.footer a {
    color: #eee;
}

div.header {
    margin: 0 -15px 0 -15px;
    background: url(headerbg.png) repeat-x;
    border-top: 6px solid {{ theme_headerbordercolor }};
}

div.relnav {
    border-bottom: 1px solid #111;
    background: url(navigation.png);
    margin: 0 -15px 0 -15px;
    padding: 2px 20px 0 28px;
    line-height: 25px;
    color: #aaa;
    font-size: 12px;
    text-align: center;
}

div.relnav a {
    color: #eee;
    font-weight: bold;
    text-decoration: none;
}

div.relnav a:hover {
    text-decoration: underline;
}

#content {
    background-color: white;
    color: #111;
    border-bottom: 1px solid black;
    background: url(watermark.png) center 0;
    padding: 0 15px 0 15px;
    margin: 0;
}

h1 {
    margin: 0;
    padding: 15px 0 0 0;
}

h1.heading {
    margin: 0;
    padding: 0;
    height: 80px;
}

h1.heading:hover {
    background: #222;
}

h1.heading a {
    background: url({{ logo if logo else 'logo.png' }}) no-repeat center 0;
    display: block;
    width: 100%;
    height: 80px;
}

h1.heading a:focus {
    -moz-outline: none;
    outline: none;
}

h1.heading span {
    display: none;
}

#contentwrapper {
    min-width: {{ theme_body_min_width|todim }};
    max-width: {{ theme_body_max_width|todim }};
    padding: 0 18px 20px 18px;
    margin: 0 auto 0 auto;
    border-right: 1px solid #eee;
    border-left: 1px solid #eee;
    background: url(watermark_blur.png) center -114px;
}

#contentwrapper h2,
#contentwrapper h2 a {
    color: #222;
    font-size: 24px;
    margin: 20px 0 0 0;
}

#contentwrapper h3,
#contentwrapper h3 a {
    color: {{ theme_subheadlinecolor }};
    font-size: 20px;
    margin: 20px 0 0 0;
}

table.docutils {
    border-collapse: collapse;
    border: 2px solid #aaa;
    margin: 0.5em 1.5em 0.5em 1.5em;
}

table.docutils td {
    padding: 2px;
    border: 1px solid #ddd;
}

p, li, dd, dt, blockquote {
    color: #333;
}

blockquote {
    margin: 10px 0 10px 20px;
}

p {
    line-height: 20px;
    margin-bottom: 0;
    margin-top: 10px;
}

hr {
    border-top: 1px solid #ccc;
    border-bottom: 0;
    border-right: 0;
    border-left: 0;
    margin-bottom: 10px;
    margin-top: 20px;
}

dl {
    margin-left: 10px;
}

li, dt {
    margin-top: 5px;
}

dt {
    font-weight: bold;
    color: #000;
}

dd {
    margin-top: 10px;
    line-height: 20px;
}

th {
    text-align: left;
    padding: 3px;
    background-color: #f2f2f2;
}

a {
    color: {{ theme_linkcolor }};
}

a:hover {
    color: {{ theme_visitedlinkcolor }};
}

a:visited {
    color: {{ theme_visitedlinkcolor }};
}

pre {
    background-image: url(metal.png);
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    padding: 5px;
    font-size: 13px;
    font-family: 'Bitstream Vera Sans Mono', 'Monaco', monospace;
}

code {
    font-size: 13px;
    font-family: 'Bitstream Vera Sans Mono', 'Monaco', monospace;
    color: black;
    padding: 1px 2px 1px 2px;
    background-color: #fafafa;
    border-bottom: 1px solid #eee;
}

div.code-block-caption {
    color: #efefef;
    background-color: #888;
}

div.code-block-caption span.caption-number {
    padding: 0.1em 0.3em;
    font-style: italic;
}

div.code-block-caption span.caption-text {
}

div.literal-block-wrapper {
    padding: 1em 1em 0;
}

div.literal-block-wrapper pre {
    margin: 0;
}

a.reference:hover code {
    border-bottom-color: #aaa;
}

cite {
    /* abusing <cite>, it's generated by ReST for `x` */
    font-size: 13px;
    font-family: 'Bitstream Vera Sans Mono', 'Monaco', monospace;
    font-weight: bold;
    font-style: normal;
}

div.admonition {
    margin: 10px 0 10px 0;
    padding: 10px;
    border: 1px solid #ccc;
}

div.admonition p.admonition-title {
    background-color: {{ theme_admonitioncolor }};
    color: white;
    margin: -10px -10px 10px -10px;
    padding: 4px 10px 4px 10px;
    font-weight: bold;
    font-size: 15px;
}

div.admonition p.admonition-title a {
    color: white!important;
}

a.headerlink {
    color: #B4B4B4!important;
    font-size: 0.8em;
    padding: 0 4px 0 4px;
    text-decoration: none!important;
    visibility: hidden;
}

h1:hover > a.headerlink,
h2:hover > a.headerlink,
h3:hover > a.headerlink,
h4:hover > a.headerlink,
h5:hover > a.headerlink,
h6:hover > a.headerlink,
dt:hover > a.headerlink,
dt:hover > a.headerlink,
caption:hover > a.headerlink,
p.caption:hover > a.headerlink,
div.code-block-caption:hover > a.headerlink {
    visibility: visible;
}

a.headerlink:hover {
    background-color: #B4B4B4;
    color: #F0F0F0!important;
}

table caption span.caption-number {
    font-style: italic;
}

table caption span.caption-text {
}

table.indextable {
    width: 100%;
}

table.genindextable td {
    vertical-align: top;
    width: 50%;
}

table.indextable ul {
    margin-top: 0;
    margin-bottom: 0;
    list-style-type: none;
    font-size: 11px;
}

table.indextable ul a {
    color: #000;
}

table.indextable > tbody > tr > td > ul {
    padding-left: 0em;
}

div.modindex-jumpbox {
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    margin: 1em 0 1em 0;
    padding: 0.4em;
}

table.modindextable {
    width: 100%;
    border: none;
}

table.modindextable td {
    padding: 2px;
    border-collapse: collapse;
}

table.modindextable img.toggler {
    margin-right: 10px;
}

dl.function dt,
dl.class dt,
dl.exception dt,
dl.method dt,
dl.attribute dt {
    font-weight: normal;
}

dt .descname {
    font-weight: bold;
    margin-right: 4px;
}

dt .sig-paren {
    font-size: larger;
}

dt .descname, dt .descclassname {
    padding: 0;
    background: transparent;
    border-bottom: 1px solid #111;
}

dt .descclassname {
    margin-left: 2px;
}

dl dt big {
    font-size: 100%;
}

ul.search {
    margin: 10px 0 0 30px;
    padding: 0;
}

ul.search li {
    margin: 10px 0 0 0;
    padding: 0;
}

ul.search div.context {
    font-size: 12px;
    padding: 4px 0 0 20px;
    color: #888;
}

span.highlight {
    background-color: #eee;
    border: 1px solid #ccc;
}

#toc {
    margin: 0 -17px 0 -17px;
    display: none;
}

#toc h3 {
    float: right;
    margin: 5px 5px 0 0;
    padding: 0;
    font-size: 12px;
    color: #777;
}

#toc h3:hover {
    color: #333;
    cursor: pointer;
}

.expandedtoc {
    background: #222 url(darkmetal.png);
    border-bottom: 1px solid #111;
    outline-bottom: 1px solid #000;
    padding: 5px;
}

.expandedtoc h3 {
    color: #aaa;
    margin: 0!important;
}

.expandedtoc h3:hover {
    color: white!important;
}

#tod h3:hover {
    color: white;
}

#toc a {
    color: #ddd;
    text-decoration: none;
}

#toc a:hover {
    color: white;
    text-decoration: underline;
}

#toc ul {
    margin: 5px 0 12px 17px;
    padding: 0 7px 0 7px;
}

#toc ul ul {
    margin-bottom: 0;
}

#toc ul li {
    margin: 2px 0 0 0;
}

.line-block {
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
}

.line-block .line-block {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 1.5em;
}

.viewcode-link {
    float: right;
}

.viewcode-back {
    float: right;
    font-family: 'Georgia', serif;
}

div.viewcode-block:target {
    background-color: #f4debf;
    border-top: 1px solid #ac9;
    border-bottom: 1px solid #ac9;
    margin: -1px -5px;
    padding: 0 5px;
}

div.figure p.caption span.caption-number,
figcaption span.caption-number {
    font-style: italic;
}

div.figure p.caption span.caption-text,
figcaption span.caption-text {
}

/* math display */

div.math p {
    text-align: center;
}

span.eqno {
    float: right;
}
