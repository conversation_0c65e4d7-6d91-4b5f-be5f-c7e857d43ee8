# Translations template for sphinxcontrib-applehelp.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the
# sphinxcontrib-applehelp project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2019
# 
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: sphinxcontrib-applehelp 1.0.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2019-01-20 16:59+0900\n"
"PO-Revision-Date: 2019-01-20 07:59+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Chinese (China) (https://www.transifex.com/sphinx-doc/teams/36659/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.6.0\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: sphinxcontrib/applehelp/__init__.py:39
msgid "Help indexer failed"
msgstr "构建帮助文件索引失败"

#: sphinxcontrib/applehelp/__init__.py:43
msgid "Code signing failed"
msgstr "代码签名失败"

#: sphinxcontrib/applehelp/__init__.py:52
#, python-format
msgid ""
"The help book is in %(outdir)s.\n"
"Note that won't be able to view it unless you put it in ~/Library/Documentation/Help or install it in your application bundle."
msgstr ""
"帮助文件存放在 %(outdir)s 中。\n"
"注意你现在无法直接调用它，你需要把它放到 ~/Library/Documentation/Help 目录下或是安装到应用捆绑中。"

#: sphinxcontrib/applehelp/__init__.py:79
msgid "You must set applehelp_bundle_id before building Apple Help output"
msgstr "构建 Apple 帮助文件输出前必须设置 applehelp_bundle_id"

#: sphinxcontrib/applehelp/__init__.py:96
msgid "copying localized files"
msgstr ""

#: sphinxcontrib/applehelp/__init__.py:123
msgid "writing Info.plist"
msgstr ""

#: sphinxcontrib/applehelp/__init__.py:161
msgid "copying icon... "
msgstr "复制图标……"

#: sphinxcontrib/applehelp/__init__.py:165
#, python-format
msgid "cannot copy icon file %r: %s"
msgstr "无法复制图标文件 %r：%s"

#: sphinxcontrib/applehelp/__init__.py:167
msgid "building access page"
msgstr ""

#: sphinxcontrib/applehelp/__init__.py:177
msgid "generating help index"
msgstr ""

#: sphinxcontrib/applehelp/__init__.py:201
#, python-format
msgid ""
"you will need to index this help book with:\n"
"  %s"
msgstr ""
"索引此帮助文件需要：\n"
"%s"

#: sphinxcontrib/applehelp/__init__.py:207
#: sphinxcontrib/applehelp/__init__.py:232
#, python-format
msgid "Command not found: %s"
msgstr "命令不存在：%s"

#: sphinxcontrib/applehelp/__init__.py:211
msgid "signing help book"
msgstr ""

#: sphinxcontrib/applehelp/__init__.py:226
#, python-format
msgid ""
"you will need to sign this help book with:\n"
"  %s"
msgstr ""
"签名帮助文件需要：\n"
"%s"
