# Translations template for sphinxcontrib-htmlhelp.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the
# sphinxcontrib-htmlhelp project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# BouRock, 2020
# 
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: sphinxcontrib-htmlhelp 1.0.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2019-02-15 00:45+0900\n"
"PO-Revision-Date: 2019-02-14 15:47+0000\n"
"Last-Translator: BouRock, 2020\n"
"Language-Team: Turkish (https://www.transifex.com/sphinx-doc/teams/36659/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.6.0\n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: sphinxcontrib/htmlhelp/__init__.py:147
#, python-format
msgid "You can now run HTML Help Workshop with the .htp file in %(outdir)s."
msgstr ""
"Artık %(outdir)s içindeki .htp dosyası ile HTML Yardım Atölyesi'ni "
"çalıştırabilirsiniz."

#: sphinxcontrib/htmlhelp/__init__.py:203
msgid "copying stopword list"
msgstr "gereksiz kelime listesi kopyalanıyor"

#: sphinxcontrib/htmlhelp/__init__.py:218
msgid "writing project file"
msgstr "proje dosyası yazılıyor"

#: sphinxcontrib/htmlhelp/__init__.py:246
msgid "writing TOC file"
msgstr "TOC dosyası yazılıyor"

#: sphinxcontrib/htmlhelp/__init__.py:268
msgid "writing index file..."
msgstr "dizin dosyası yazılıyor..."
