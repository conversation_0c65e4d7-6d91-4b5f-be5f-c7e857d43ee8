/*
 * Sphinx stylesheet -- epub theme.
 */

/* -- main layout ----------------------------------------------------------- */

{% if theme_writing_mode is defined %}
body{
  writing-mode: {{ theme_writing_mode }};
  line-break: normal;
  -epub-writing-mode: {{ theme_writing_mode }};
  -webkit-writing-mode: {{ theme_writing_mode }};
  -epub-line-break: normal;
  -webkit-line-break: normal;
}
{% endif %}

div.clearer {
    clear: both;
}

a:link {
    color: #3333ff;
    text-decoration: underline;
}

a:visited {
    color: #551a8b;
    text-decoration: underline;
} 

img {
    border: 0;
    max-width: 100%;
}

/* -- relbar ---------------------------------------------------------------- */

div.related {
    width: 100%;
    font-family: sans-serif;
    font-size: 90%;
}

div.related h3 {
    display: none;
}

div.related ul {
    margin: 0;
    padding: 0 0 0 10px;
    list-style: none;
}

div.related li {
    display: inline;
}

div.related li.right {
    float: right;
    margin-right: 5px;
}

/* -- sidebar --------------------------------------------------------------- */

div.sphinxsidebarwrapper {
    padding: 10px 5px 0 10px;
}

div.sphinxsidebar {
    float: left;
    width: 230px;
    margin-left: -100%;
    font-size: 90%;
}

div.sphinxsidebar ul {
    list-style: none;
}

div.sphinxsidebar ul ul,
div.sphinxsidebar ul.want-points {
    margin-left: 20px;
    list-style: square;
}

div.sphinxsidebar ul ul {
    margin-top: 0;
    margin-bottom: 0;
}

div.sphinxsidebar form {
    margin-top: 10px;
}

div.sphinxsidebar input {
    border: 1px solid #98dbcc;
    font-family: sans-serif;
    font-size: 100%;
}

img {
    border: 0;
    max-width: 100%;
}

/* -- search page ----------------------------------------------------------- */

ul.search {
    margin: 10px 0 0 20px;
    padding: 0;
}

ul.search li {
    padding: 5px 0 5px 20px;
    background-image: url(file.png);
    background-repeat: no-repeat;
    background-position: 0 7px;
}

ul.search li a {
    font-weight: bold;
}

ul.search li div.context {
    color: #888;
    margin: 2px 0 0 30px;
    text-align: left;
}

ul.keywordmatches li.goodmatch a {
    font-weight: bold;
}

/* -- index page ------------------------------------------------------------ */

table.contentstable {
    width: 90%;
}

table.contentstable p.biglink {
    line-height: 150%;
}

a.biglink {
    font-size: 130%;
}

span.linkdescr {
    font-style: italic;
    padding-top: 5px;
    font-size: 90%;
}

/* -- general index --------------------------------------------------------- */

table.indextable td {
    text-align: left;
    vertical-align: top;
}

table.indextable ul {
    margin-top: 0;
    margin-bottom: 0;
    list-style-type: none;
}

table.indextable > tbody > tr > td > ul {
    padding-left: 0em;
}

table.indextable tr.pcap {
    height: 10px;
}

table.indextable tr.cap {
    margin-top: 10px;
    background-color: #f2f2f2;
}

img.toggler {
    margin-right: 3px;
    margin-top: 3px;
    cursor: pointer;
}

/* -- domain module index --------------------------------------------------- */

table.modindextable td {
    padding: 2px;
    border-collapse: collapse;
}

/* -- general body styles --------------------------------------------------- */

a.headerlink {
    visibility: hidden;
}

div.body p.caption {
    text-align: inherit;
}

div.body td {
    text-align: left;
}

.first {
    margin-top: 0 !important;
}

p.rubric {
    margin-top: 30px;
    font-weight: bold;
}

.align-left {
    text-align: left;
}

.align-center {
    text-align: center;
}

.align-right {
    text-align: right;
}

/* -- sidebars -------------------------------------------------------------- */

div.sidebar, aside.sidebar {
    margin: 0 0 0.5em 1em;
    border: 1px solid #ddb;
    padding: 7px 7px 0 7px;
    background-color: #ffe;
    width: 40%;
    float: right;
}

p.sidebar-title {
    font-weight: bold;
}

/* -- topics ---------------------------------------------------------------- */

nav.contents,
aside.topic,
div.topic {
    border: 1px solid #ccc;
    padding: 7px 7px 0 7px;
    margin: 10px 0 10px 0;
}

p.topic-title {
    font-size: 110%;
    font-weight: bold;
    margin-top: 10px;
}

/* -- admonitions ----------------------------------------------------------- */

div.admonition {
    margin-top: 10px;
    margin-bottom: 10px;
    padding: 7px;
}

div.admonition dt {
    font-weight: bold;
}

div.admonition dl {
    margin-bottom: 0;
}

p.admonition-title {
    margin: 0px 10px 5px 0px;
    font-weight: bold;
}

div.body p.centered {
    text-align: center;
    margin-top: 25px;
}

/* -- tables ---------------------------------------------------------------- */

table.docutils {
    border: 0;
    border-collapse: collapse;
}

table caption span.caption-number {
    font-style: italic;
}

table caption span.caption-text {
}

table.docutils td, table.docutils th {
    padding: 1px 8px 1px 5px;
    border-top: 0;
    border-left: 0;
    border-right: 0;
    border-bottom: 1px solid #aaa;
}

table.footnote td, table.footnote th {
    border: 0 !important;
}

th {
    text-align: left;
    padding-right: 5px;
}

table.citation {
    border-left: solid 1px gray;
    margin-left: 1px;
}

table.citation td {
    border-bottom: none;
}

/* -- figures --------------------------------------------------------------- */

div.figure p.caption span.caption-number,
figcaption span.caption-number {
    font-style: italic;
}

div.figure p.caption span.caption-text,
figcaption span.caption-text {
}

/* -- field list styles ----------------------------------------------------- */

/* -- for html4 -- */

table.field-list td, table.field-list th {
    border: 0 !important;
}

.field-list ul {
    margin: 0;
    padding-left: 1em;
}

.field-list p {
    margin: 0;
}

/* -- for html5 -- */

/* bold field name, content starts on the same line */

dl.field-list > dt,
dl.option-list > dt,
dl.docinfo > dt,
dl.footnote > dt,
dl.citation > dt {
    font-weight: bold;
    clear: left;
    float: left;
    margin: 0;
    padding: 0;
    padding-right: 0.5em;
}

/* Offset for field content (corresponds to the --field-name-limit option) */

dl.field-list > dd,
dl.option-list > dd,
dl.docinfo > dd {
    margin-left:  9em; /* ca. 14 chars in the test examples */
}

/* start field-body on a new line after long field names */

dl.field-list > dd > *:first-child,
dl.option-list > dd > *:first-child
{
    display: inline-block;
    width: 100%;
    margin: 0;
}

dl.field-list > dt:after,
dl.docinfo > dt:after {
    content: ":";
}

/* -- option lists ---------------------------------------------------------- */

dl.option-list {
    margin-left: 40px;
}

dl.option-list > dt {
    font-weight: normal;
}

span.option {
    white-space: nowrap;
}

/* -- lists ----------------------------------------------------------------- */

/* -- compact and simple lists: no margin between items -- */

.simple  li, .compact li,
.simple  ul, .compact ul,
.simple  ol, .compact ol,
.simple > li p, .compact > li p,
dl.simple > dd, dl.compact > dd {
    margin-top: 0;
    margin-bottom: 0;
}

/* -- enumerated lists ------------------------------------------------------ */

ol.arabic {
    list-style: decimal;
}

ol.loweralpha {
    list-style: lower-alpha;
}

ol.upperalpha {
    list-style: upper-alpha;
}

ol.lowerroman {
    list-style: lower-roman;
}

ol.upperroman {
    list-style: upper-roman;
}

dt span.classifier {
    font-style: italic;
}

dt span.classifier:before {
    font-style: normal;
    margin: 0.5em;
    content: ":";
}

/* -- other body styles ----------------------------------------------------- */

dl {
    margin-bottom: 15px;
}

.sig dl {
    margin-top: 0px;
    margin-bottom: 0px;
}

dd p {
    margin-top: 0px;
}

dd ul, dd table {
    margin-bottom: 10px;
}

dd {
    margin-top: 3px;
    margin-bottom: 10px;
    margin-left: 30px;
}

.sig dd {
    margin-top: 0px;
    margin-bottom: 0px;
}

dt:target, .highlighted {
    background-color: #ddd;
}

dl.glossary dt {
    font-weight: bold;
    font-size: 110%;
}

.optional {
    font-size: 130%;
}

.sig-paren {
    font-size: larger;
}

.versionmodified {
    font-style: italic;
}

.system-message {
    background-color: #fda;
    padding: 5px;
    border: 3px solid red;
}

/* -- footnotes and citations ----------------------------------------------- */

/* -- for html4 -- */
.footnote:target  {
    background-color: #dddddd;
}

/* -- for html5 -- */

dl.footnote.superscript > dd {
    margin-left: 1em;
}

dl.footnote.brackets > dd {
    margin-left: 2em;
}

dl > dt.label {
    font-weight: normal;
}

a.footnote-reference.brackets:before,
dt.label > span.brackets:before {
    content: "[";
}

a.footnote-reference.brackets:after,
dt.label > span.brackets:after {
    content: "]";
}

a.footnote-reference.superscript,
dl.footnote.superscript > dt.label {
    vertical-align: super;
    font-size: smaller;
}

dt.label > span.fn-backref {
    margin-left: 0.2em;
}

dt.label > span.fn-backref > a {
    font-style: italic;
}

/* -- line blocks ----------------------------------------------------------- */

.line-block {
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
}

.line-block .line-block {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 1.5em;
}

.guilabel, .menuselection {
    font-style: italic;
}

.accelerator {
    text-decoration: underline;
}

.classifier {
    font-style: oblique;
}

abbr, acronym {
    border-bottom: dotted 1px;
    cursor: help;
}

/* -- code displays --------------------------------------------------------- */

pre {
    font-family: monospace;
    overflow: auto;
    overflow-y: hidden;
}

td.linenos pre {
    padding: 5px 0px;
    border: 0;
    background-color: transparent;
    color: #aaa;
}

table.highlighttable {
    margin-left: 0.5em;
}

table.highlighttable td {
    padding: 0 0.5em 0 0.5em;
}

code {
    font-family: monospace;
}

div.code-block-caption span.caption-number {
    padding: 0.1em 0.3em;
    font-style: italic;
}

div.code-block-caption span.caption-text {
}

div.literal-block-wrapper {
    padding: 1em 1em 0;
}

div.literal-block-wrapper div.highlight {
    margin: 0;
}

code.descname {
    background-color: transparent;
    font-weight: bold;
    font-size: 1.2em;
}

code.descclassname {
    background-color: transparent;
}

code.xref, a code {
    background-color: transparent;
    font-weight: bold;
}

h1 code, h2 code, h3 code, h4 code, h5 code, h6 code {
    background-color: transparent;
}

/* -- math display ---------------------------------------------------------- */

img.math {
    vertical-align: middle;
}

div.body div.math p {
    text-align: center;
}

span.eqno {
    float: right;
}

/* -- special divs  --------------------------------------------------------- */

div.quotebar {
    background-color: #e3eff1;
    max-width: 250px;
    float: right;
    font-family: sans-serif;
    padding: 7px 7px;
    border: 1px solid #ccc;
}
div.footer {
    background-color: #e3eff1;
    padding: 3px 8px 3px 0;
    clear: both;
    font-family: sans-serif;
    font-size: 80%;
    text-align: right;
}

div.footer a {
    text-decoration: underline;
}

/* -- link-target ----------------------------------------------------------- */

.link-target {
    font-size: 80%;
}

table .link-target {
    /* Do not show links in tables, there is not enough space */
    display: none;
}

/* -- font-face ------------------------------------------------------------- */

/*
@font-face {
    font-family: "LiberationNarrow";
    font-style: normal;
    font-weight: normal;
    src: url("res:///Data/fonts/LiberationNarrow-Regular.otf")
        format("opentype");
}
@font-face {
    font-family: "LiberationNarrow";
    font-style: oblique, italic;
    font-weight: normal;
    src: url("res:///Data/fonts/LiberationNarrow-Italic.otf")
        format("opentype");
}
@font-face {
    font-family: "LiberationNarrow";
    font-style: normal;
    font-weight: bold;
    src: url("res:///Data/fonts/LiberationNarrow-Bold.otf")
        format("opentype");
}
@font-face {
    font-family: "LiberationNarrow";
    font-style: oblique, italic;
    font-weight: bold;
    src: url("res:///Data/fonts/LiberationNarrow-BoldItalic.otf")
        format("opentype");
}
*/
