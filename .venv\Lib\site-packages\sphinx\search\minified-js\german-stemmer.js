GermanStemmer=function(){var r=new BaseStemmer;var e=[["",-1,5],["U",0,2],["Y",0,1],["ä",0,3],["ö",0,4],["ü",0,2]];var i=[["e",-1,2],["em",-1,1],["en",-1,2],["ern",-1,1],["er",-1,1],["s",-1,3],["es",5,2]];var s=[["en",-1,1],["er",-1,1],["st",-1,2],["est",2,1]];var u=[["ig",-1,1],["lich",-1,1]];var a=[["end",-1,1],["ig",-1,2],["ung",-1,1],["lich",-1,3],["isch",-1,2],["ik",-1,2],["heit",-1,3],["keit",-1,4]];var c=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,8,0,32,8];var t=[117,30,5];var o=[117,30,4];var f=0;var l=0;var n=0;function b(){var e=r.cursor;while(true){var i=r.cursor;r:{e:{var s=r.cursor;i:{r.bra=r.cursor;if(!r.eq_s("ß")){break i}r.ket=r.cursor;if(!r.slice_from("ss")){return false}break e}r.cursor=s;if(r.cursor>=r.limit){break r}r.cursor++}continue}r.cursor=i;break}r.cursor=e;while(true){var u=r.cursor;r:{e:while(true){var a=r.cursor;i:{if(!r.in_grouping(c,97,252)){break i}r.bra=r.cursor;s:{var t=r.cursor;u:{if(!r.eq_s("u")){break u}r.ket=r.cursor;if(!r.in_grouping(c,97,252)){break u}if(!r.slice_from("U")){return false}break s}r.cursor=t;if(!r.eq_s("y")){break i}r.ket=r.cursor;if(!r.in_grouping(c,97,252)){break i}if(!r.slice_from("Y")){return false}}r.cursor=a;break e}r.cursor=a;if(r.cursor>=r.limit){break r}r.cursor++}continue}r.cursor=u;break}return true}function k(){n=r.limit;l=r.limit;var e=r.cursor;{var i=r.cursor+3;if(i>r.limit){return false}r.cursor=i}f=r.cursor;r.cursor=e;r:while(true){e:{if(!r.in_grouping(c,97,252)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}r:while(true){e:{if(!r.out_grouping(c,97,252)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}n=r.cursor;r:{if(!(n<f)){break r}n=f}r:while(true){e:{if(!r.in_grouping(c,97,252)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}r:while(true){e:{if(!r.out_grouping(c,97,252)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}l=r.cursor;return true}function m(){var i;while(true){var s=r.cursor;r:{r.bra=r.cursor;i=r.find_among(e);if(i==0){break r}r.ket=r.cursor;switch(i){case 1:if(!r.slice_from("y")){return false}break;case 2:if(!r.slice_from("u")){return false}break;case 3:if(!r.slice_from("a")){return false}break;case 4:if(!r.slice_from("o")){return false}break;case 5:if(r.cursor>=r.limit){break r}r.cursor++;break}continue}r.cursor=s;break}return true}function _(){if(!(n<=r.cursor)){return false}return true}function v(){if(!(l<=r.cursor)){return false}return true}function g(){var e;var c=r.limit-r.cursor;r:{r.ket=r.cursor;e=r.find_among_b(i);if(e==0){break r}r.bra=r.cursor;if(!_()){break r}switch(e){case 1:if(!r.slice_del()){return false}break;case 2:if(!r.slice_del()){return false}var f=r.limit-r.cursor;e:{r.ket=r.cursor;if(!r.eq_s_b("s")){r.cursor=r.limit-f;break e}r.bra=r.cursor;if(!r.eq_s_b("nis")){r.cursor=r.limit-f;break e}if(!r.slice_del()){return false}}break;case 3:if(!r.in_grouping_b(t,98,116)){break r}if(!r.slice_del()){return false}break}}r.cursor=r.limit-c;var l=r.limit-r.cursor;r:{r.ket=r.cursor;e=r.find_among_b(s);if(e==0){break r}r.bra=r.cursor;if(!_()){break r}switch(e){case 1:if(!r.slice_del()){return false}break;case 2:if(!r.in_grouping_b(o,98,116)){break r}{var n=r.cursor-3;if(n<r.limit_backward){break r}r.cursor=n}if(!r.slice_del()){return false}break}}r.cursor=r.limit-l;var b=r.limit-r.cursor;r:{r.ket=r.cursor;e=r.find_among_b(a);if(e==0){break r}r.bra=r.cursor;if(!v()){break r}switch(e){case 1:if(!r.slice_del()){return false}var k=r.limit-r.cursor;e:{r.ket=r.cursor;if(!r.eq_s_b("ig")){r.cursor=r.limit-k;break e}r.bra=r.cursor;{var m=r.limit-r.cursor;i:{if(!r.eq_s_b("e")){break i}r.cursor=r.limit-k;break e}r.cursor=r.limit-m}if(!v()){r.cursor=r.limit-k;break e}if(!r.slice_del()){return false}}break;case 2:{var g=r.limit-r.cursor;e:{if(!r.eq_s_b("e")){break e}break r}r.cursor=r.limit-g}if(!r.slice_del()){return false}break;case 3:if(!r.slice_del()){return false}var d=r.limit-r.cursor;e:{r.ket=r.cursor;i:{var h=r.limit-r.cursor;s:{if(!r.eq_s_b("er")){break s}break i}r.cursor=r.limit-h;if(!r.eq_s_b("en")){r.cursor=r.limit-d;break e}}r.bra=r.cursor;if(!_()){r.cursor=r.limit-d;break e}if(!r.slice_del()){return false}}break;case 4:if(!r.slice_del()){return false}var w=r.limit-r.cursor;e:{r.ket=r.cursor;if(r.find_among_b(u)==0){r.cursor=r.limit-w;break e}r.bra=r.cursor;if(!v()){r.cursor=r.limit-w;break e}if(!r.slice_del()){return false}}break}}r.cursor=r.limit-b;return true}this.stem=function(){var e=r.cursor;b();r.cursor=e;var i=r.cursor;k();r.cursor=i;r.limit_backward=r.cursor;r.cursor=r.limit;g();r.cursor=r.limit_backward;var s=r.cursor;m();r.cursor=s;return true};this["stemWord"]=function(e){r.setCurrent(e);this.stem();return r.getCurrent()}};