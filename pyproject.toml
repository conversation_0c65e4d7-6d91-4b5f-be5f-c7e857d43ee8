[project]
name = "sgbuilder"
version = "0.1.0"
description = "A Python package for building something great"
readme = "README.md"
requires-python = ">=3.11"
dependencies = []
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
]

[project.scripts]
sgbuilder = "main:main"

[project.optional-dependencies]
docs = ["sphinx"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
