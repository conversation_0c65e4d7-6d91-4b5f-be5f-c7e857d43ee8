# Translations template for sphinxcontrib-serializinghtml.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the
# sphinxcontrib-serializinghtml project.
# <AUTHOR> <EMAIL>, 2019.
# 
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: sphinxcontrib-serializinghtml 1.0.1\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2019-02-17 21:23+0900\n"
"PO-Revision-Date: 2019-02-17 12:23+0000\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/sphinx-doc/teams/36659/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.6.0\n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: sphinxcontrib/serializinghtml/__init__.py:137
#, python-format
msgid "You can now process the pickle files in %(outdir)s."
msgstr ""

#: sphinxcontrib/serializinghtml/__init__.py:154
#, python-format
msgid "You can now process the JSON files in %(outdir)s."
msgstr ""
