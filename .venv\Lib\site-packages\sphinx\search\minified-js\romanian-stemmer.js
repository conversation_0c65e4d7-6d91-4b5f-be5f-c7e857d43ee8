RomanianStemmer=function(){var r=new BaseStemmer;var i=[["",-1,3],["I",0,1],["U",0,2]];var e=[["ea",-1,3],["aţia",-1,7],["aua",-1,2],["iua",-1,4],["aţie",-1,7],["ele",-1,3],["ile",-1,5],["iile",6,4],["iei",-1,4],["atei",-1,6],["ii",-1,4],["ului",-1,1],["ul",-1,1],["elor",-1,3],["ilor",-1,4],["iilor",14,4]];var a=[["icala",-1,4],["iciva",-1,4],["ativa",-1,5],["itiva",-1,6],["icale",-1,4],["aţiune",-1,5],["iţiune",-1,6],["atoare",-1,5],["itoare",-1,6],["ătoare",-1,5],["icitate",-1,4],["abilitate",-1,1],["ibilitate",-1,2],["ivitate",-1,3],["icive",-1,4],["ative",-1,5],["itive",-1,6],["icali",-1,4],["atori",-1,5],["icatori",18,4],["itori",-1,6],["ători",-1,5],["icitati",-1,4],["abilitati",-1,1],["ivitati",-1,3],["icivi",-1,4],["ativi",-1,5],["itivi",-1,6],["icităi",-1,4],["abilităi",-1,1],["ivităi",-1,3],["icităţi",-1,4],["abilităţi",-1,1],["ivităţi",-1,3],["ical",-1,4],["ator",-1,5],["icator",35,4],["itor",-1,6],["ător",-1,5],["iciv",-1,4],["ativ",-1,5],["itiv",-1,6],["icală",-1,4],["icivă",-1,4],["ativă",-1,5],["itivă",-1,6]];var t=[["ica",-1,1],["abila",-1,1],["ibila",-1,1],["oasa",-1,1],["ata",-1,1],["ita",-1,1],["anta",-1,1],["ista",-1,3],["uta",-1,1],["iva",-1,1],["ic",-1,1],["ice",-1,1],["abile",-1,1],["ibile",-1,1],["isme",-1,3],["iune",-1,2],["oase",-1,1],["ate",-1,1],["itate",17,1],["ite",-1,1],["ante",-1,1],["iste",-1,3],["ute",-1,1],["ive",-1,1],["ici",-1,1],["abili",-1,1],["ibili",-1,1],["iuni",-1,2],["atori",-1,1],["osi",-1,1],["ati",-1,1],["itati",30,1],["iti",-1,1],["anti",-1,1],["isti",-1,3],["uti",-1,1],["işti",-1,3],["ivi",-1,1],["ităi",-1,1],["oşi",-1,1],["ităţi",-1,1],["abil",-1,1],["ibil",-1,1],["ism",-1,3],["ator",-1,1],["os",-1,1],["at",-1,1],["it",-1,1],["ant",-1,1],["ist",-1,3],["ut",-1,1],["iv",-1,1],["ică",-1,1],["abilă",-1,1],["ibilă",-1,1],["oasă",-1,1],["ată",-1,1],["ită",-1,1],["antă",-1,1],["istă",-1,3],["ută",-1,1],["ivă",-1,1]];var s=[["ea",-1,1],["ia",-1,1],["esc",-1,1],["ăsc",-1,1],["ind",-1,1],["ând",-1,1],["are",-1,1],["ere",-1,1],["ire",-1,1],["âre",-1,1],["se",-1,2],["ase",10,1],["sese",10,2],["ise",10,1],["use",10,1],["âse",10,1],["eşte",-1,1],["ăşte",-1,1],["eze",-1,1],["ai",-1,1],["eai",19,1],["iai",19,1],["sei",-1,2],["eşti",-1,1],["ăşti",-1,1],["ui",-1,1],["ezi",-1,1],["âi",-1,1],["aşi",-1,1],["seşi",-1,2],["aseşi",29,1],["seseşi",29,2],["iseşi",29,1],["useşi",29,1],["âseşi",29,1],["işi",-1,1],["uşi",-1,1],["âşi",-1,1],["aţi",-1,2],["eaţi",38,1],["iaţi",38,1],["eţi",-1,2],["iţi",-1,2],["âţi",-1,2],["arăţi",-1,1],["serăţi",-1,2],["aserăţi",45,1],["seserăţi",45,2],["iserăţi",45,1],["userăţi",45,1],["âserăţi",45,1],["irăţi",-1,1],["urăţi",-1,1],["ârăţi",-1,1],["am",-1,1],["eam",54,1],["iam",54,1],["em",-1,2],["asem",57,1],["sesem",57,2],["isem",57,1],["usem",57,1],["âsem",57,1],["im",-1,2],["âm",-1,2],["ăm",-1,2],["arăm",65,1],["serăm",65,2],["aserăm",67,1],["seserăm",67,2],["iserăm",67,1],["userăm",67,1],["âserăm",67,1],["irăm",65,1],["urăm",65,1],["ârăm",65,1],["au",-1,1],["eau",76,1],["iau",76,1],["indu",-1,1],["ându",-1,1],["ez",-1,1],["ească",-1,1],["ară",-1,1],["seră",-1,2],["aseră",84,1],["seseră",84,2],["iseră",84,1],["useră",84,1],["âseră",84,1],["iră",-1,1],["ură",-1,1],["âră",-1,1],["ează",-1,1]];var u=[["a",-1,1],["e",-1,1],["ie",1,1],["i",-1,1],["ă",-1,1]];var c=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,0,2,32,0,0,4];var o=false;var f=0;var l=0;var n=0;function b(){while(true){var i=r.cursor;r:{i:while(true){var e=r.cursor;e:{if(!r.in_grouping(c,97,259)){break e}r.bra=r.cursor;a:{var a=r.cursor;t:{if(!r.eq_s("u")){break t}r.ket=r.cursor;if(!r.in_grouping(c,97,259)){break t}if(!r.slice_from("U")){return false}break a}r.cursor=a;if(!r.eq_s("i")){break e}r.ket=r.cursor;if(!r.in_grouping(c,97,259)){break e}if(!r.slice_from("I")){return false}}r.cursor=e;break i}r.cursor=e;if(r.cursor>=r.limit){break r}r.cursor++}continue}r.cursor=i;break}return true}function m(){n=r.limit;l=r.limit;f=r.limit;var i=r.cursor;r:{i:{var e=r.cursor;e:{if(!r.in_grouping(c,97,259)){break e}a:{var a=r.cursor;t:{if(!r.out_grouping(c,97,259)){break t}s:while(true){u:{if(!r.in_grouping(c,97,259)){break u}break s}if(r.cursor>=r.limit){break t}r.cursor++}break a}r.cursor=a;if(!r.in_grouping(c,97,259)){break e}t:while(true){s:{if(!r.out_grouping(c,97,259)){break s}break t}if(r.cursor>=r.limit){break e}r.cursor++}}break i}r.cursor=e;if(!r.out_grouping(c,97,259)){break r}e:{var t=r.cursor;a:{if(!r.out_grouping(c,97,259)){break a}t:while(true){s:{if(!r.in_grouping(c,97,259)){break s}break t}if(r.cursor>=r.limit){break a}r.cursor++}break e}r.cursor=t;if(!r.in_grouping(c,97,259)){break r}if(r.cursor>=r.limit){break r}r.cursor++}}n=r.cursor}r.cursor=i;var s=r.cursor;r:{i:while(true){e:{if(!r.in_grouping(c,97,259)){break e}break i}if(r.cursor>=r.limit){break r}r.cursor++}i:while(true){e:{if(!r.out_grouping(c,97,259)){break e}break i}if(r.cursor>=r.limit){break r}r.cursor++}l=r.cursor;i:while(true){e:{if(!r.in_grouping(c,97,259)){break e}break i}if(r.cursor>=r.limit){break r}r.cursor++}i:while(true){e:{if(!r.out_grouping(c,97,259)){break e}break i}if(r.cursor>=r.limit){break r}r.cursor++}f=r.cursor}r.cursor=s;return true}function k(){var e;while(true){var a=r.cursor;r:{r.bra=r.cursor;e=r.find_among(i);if(e==0){break r}r.ket=r.cursor;switch(e){case 1:if(!r.slice_from("i")){return false}break;case 2:if(!r.slice_from("u")){return false}break;case 3:if(r.cursor>=r.limit){break r}r.cursor++;break}continue}r.cursor=a;break}return true}function _(){if(!(n<=r.cursor)){return false}return true}function v(){if(!(l<=r.cursor)){return false}return true}function g(){if(!(f<=r.cursor)){return false}return true}function w(){var i;r.ket=r.cursor;i=r.find_among_b(e);if(i==0){return false}r.bra=r.cursor;if(!v()){return false}switch(i){case 1:if(!r.slice_del()){return false}break;case 2:if(!r.slice_from("a")){return false}break;case 3:if(!r.slice_from("e")){return false}break;case 4:if(!r.slice_from("i")){return false}break;case 5:{var a=r.limit-r.cursor;r:{if(!r.eq_s_b("ab")){break r}return false}r.cursor=r.limit-a}if(!r.slice_from("i")){return false}break;case 6:if(!r.slice_from("at")){return false}break;case 7:if(!r.slice_from("aţi")){return false}break}return true}function d(){var i;var e=r.limit-r.cursor;r.ket=r.cursor;i=r.find_among_b(a);if(i==0){return false}r.bra=r.cursor;if(!v()){return false}switch(i){case 1:if(!r.slice_from("abil")){return false}break;case 2:if(!r.slice_from("ibil")){return false}break;case 3:if(!r.slice_from("iv")){return false}break;case 4:if(!r.slice_from("ic")){return false}break;case 5:if(!r.slice_from("at")){return false}break;case 6:if(!r.slice_from("it")){return false}break}o=true;r.cursor=r.limit-e;return true}function h(){var i;o=false;while(true){var e=r.limit-r.cursor;r:{if(!d()){break r}continue}r.cursor=r.limit-e;break}r.ket=r.cursor;i=r.find_among_b(t);if(i==0){return false}r.bra=r.cursor;if(!g()){return false}switch(i){case 1:if(!r.slice_del()){return false}break;case 2:if(!r.eq_s_b("ţ")){return false}r.bra=r.cursor;if(!r.slice_from("t")){return false}break;case 3:if(!r.slice_from("ist")){return false}break}o=true;return true}function p(){var i;if(r.cursor<n){return false}var e=r.limit_backward;r.limit_backward=n;r.ket=r.cursor;i=r.find_among_b(s);if(i==0){r.limit_backward=e;return false}r.bra=r.cursor;switch(i){case 1:r:{var a=r.limit-r.cursor;i:{if(!r.out_grouping_b(c,97,259)){break i}break r}r.cursor=r.limit-a;if(!r.eq_s_b("u")){r.limit_backward=e;return false}}if(!r.slice_del()){return false}break;case 2:if(!r.slice_del()){return false}break}r.limit_backward=e;return true}function q(){r.ket=r.cursor;if(r.find_among_b(u)==0){return false}r.bra=r.cursor;if(!_()){return false}if(!r.slice_del()){return false}return true}this.stem=function(){var i=r.cursor;b();r.cursor=i;m();r.limit_backward=r.cursor;r.cursor=r.limit;var e=r.limit-r.cursor;w();r.cursor=r.limit-e;var a=r.limit-r.cursor;h();r.cursor=r.limit-a;var t=r.limit-r.cursor;r:{i:{var s=r.limit-r.cursor;e:{if(!o){break e}break i}r.cursor=r.limit-s;if(!p()){break r}}}r.cursor=r.limit-t;var u=r.limit-r.cursor;q();r.cursor=r.limit-u;r.cursor=r.limit_backward;var c=r.cursor;k();r.cursor=c;return true};this["stemWord"]=function(i){r.setCurrent(i);this.stem();return r.getCurrent()}};