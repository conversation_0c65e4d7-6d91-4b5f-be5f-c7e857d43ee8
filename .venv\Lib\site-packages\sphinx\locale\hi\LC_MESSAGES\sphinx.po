# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON><PERSON> <a<PERSON><PERSON><PERSON>@gmail.com>, 2019
# P<PERSON>an<PERSON> <PERSON> <<EMAIL>>, 2015-2016
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-12-29 22:39+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Hindi (http://app.transifex.com/sphinx-doc/sphinx-1/language/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: events.py:77
#, python-format
msgid "Event %r already present"
msgstr "%r घटना पहले से विद्यमान है"

#: events.py:370
#, python-format
msgid "Unknown event name: %s"
msgstr "अज्ञात घटना नाम: %s"

#: events.py:416
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr ""

#: application.py:190
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "स्रोत निर्देशिका (%s) नहीं मिली"

#: application.py:194
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr ""

#: application.py:198
msgid "Source directory and destination directory cannot be identical"
msgstr "स्रोत निर्देशिका और गंतव्य निर्देशिका समरूप नहीं हो सकतीं"

#: application.py:228
#, python-format
msgid "Running Sphinx v%s"
msgstr "स्फिंक्स %s संस्करण चल रहा है"

#: application.py:250
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "इस परियोजना में स्फिंक्स का कम से कम %s संस्करण चाहिए और इसलिए इस संस्करण से बनाना संभव नहीं है."

#: application.py:266
msgid "making output directory"
msgstr "परिणाम निर्देशिका बनाई जा रही है"

#: application.py:271 registry.py:452
#, python-format
msgid "while setting up extension %s:"
msgstr "%s आयाम को स्थापित करते हुए:"

#: application.py:277
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "'स्थापना' को जैसा कि अभी कोन्फ़.पाई में परिभाषित किया गया है, पाइथन से निर्देशित नहीं है. कृपया इसकी परिभाषा में परिवर्तन करके इसे निर्देश योग्य कर्म बनाएं. कोन्फ़.पाई को स्फिंक्स के आयाम की तरह व्यवहार के लिए इसकी आवश्कयता है."

#: application.py:312
#, python-format
msgid "loading translations [%s]... "
msgstr "[%s] अनुवाद पढ़ा जा रहा है..."

#: application.py:329 util/display.py:88
msgid "done"
msgstr "संपन्न"

#: application.py:331
msgid "not available for built-in messages"
msgstr "अंतर्निर्मित संदेशों में उपलब्ध नहीं है"

#: application.py:345
msgid "loading pickled environment"
msgstr "रक्षित स्थिति को लागू किया जा रहा है"

#: application.py:353
#, python-format
msgid "failed: %s"
msgstr "असफल: %s"

#: application.py:366
msgid "No builder selected, using default: html"
msgstr "किसी निर्माता को नहीं चुना गया, मानक उपयोग: एच्.टी.ऍम.एल."

#: application.py:398
msgid "build finished with problems."
msgstr ""

#: application.py:400
msgid "build succeeded."
msgstr ""

#: application.py:404
msgid ""
"build finished with problems, 1 warning (with warnings treated as errors)."
msgstr ""

#: application.py:407
msgid "build finished with problems, 1 warning."
msgstr ""

#: application.py:409
msgid "build succeeded, 1 warning."
msgstr ""

#: application.py:414
#, python-format
msgid ""
"build finished with problems, %s warnings (with warnings treated as errors)."
msgstr ""

#: application.py:417
#, python-format
msgid "build finished with problems, %s warnings."
msgstr ""

#: application.py:419
#, python-format
msgid "build succeeded, %s warnings."
msgstr ""

#: application.py:968
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "निर्देशक कक्षा #node class# %r पहले से पंजीकृत है, इसके अभ्यागत निरस्त हो जाएंगे "

#: application.py:1047
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "निर्देश %r पहले से पंजीकृत है, यह निरस्त हो जाएगा"

#: application.py:1069 application.py:1094
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "भूमिका %r पहले से पंजीकृत है, यह निरस्त हो जाएगी"

#: application.py:1644
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "%s आयाम यह घोषित नहीं करता कि यह समानांतर पाठन के लिए सुरक्षित है. यह मानते हुए की ऐसा नहीं है - कृपया आयाम के लेखक को जांच करने और स्पष्ट व्यक्त करने के लिए कहें."

#: application.py:1648
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "समानांतर पठन के लिए यह %s विस्तार अथवा आयाम सुरक्षित नहीं है | "

#: application.py:1651
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "%s आयाम यह घोषित नहीं करता कि यह समानांतर लेखन के लिए सुरक्षित है. यह मानते हुए की ऐसा नहीं है - कृपया आयाम के लेखक को जांच करने और स्पष्ट व्यक्त करने के लिए कहें."

#: application.py:1655
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "समानांतर लेखन के लिए  %s विस्तार अथवा आयाम सुरक्षित नहीं है | "

#: application.py:1663 application.py:1667
#, python-format
msgid "doing serial %s"
msgstr "%s पर काम कर रहे हैं"

#: roles.py:208
#, python-format
msgid "Common Vulnerabilities and Exposures; CVE %s"
msgstr ""

#: roles.py:231
#, python-format
msgid "invalid CVE number %s"
msgstr ""

#: roles.py:253
#, python-format
msgid "Common Weakness Enumeration; CWE %s"
msgstr ""

#: roles.py:276
#, python-format
msgid "invalid CWE number %s"
msgstr ""

#: roles.py:296
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "पाइथन अभिवृद्धि प्रस्ताव; पी.ई.पी. %s"

#: roles.py:319
#, python-format
msgid "invalid PEP number %s"
msgstr ""

#: roles.py:357
#, python-format
msgid "invalid RFC number %s"
msgstr ""

#: registry.py:144
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "निर्माण वर्ग %s का कोई \"नाम\" भाव नहीं है"

#: registry.py:146
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "निर्माता %r पहले से (%s प्रभाग में) उपलब्ध है"

#: registry.py:159
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "निर्माता नाम %s पंजीकृत नहीं है अथवा प्रवेश स्थान पर उपलब्ध नहीं है."

#: registry.py:166
#, python-format
msgid "Builder name %s not registered"
msgstr "निर्माता नाम %s पंजीकृत नहीं है"

#: registry.py:173
#, python-format
msgid "domain %s already registered"
msgstr "अधिकारक्षेत्र %s पहले से पंजीकृत है"

#: registry.py:196 registry.py:209 registry.py:220
#, python-format
msgid "domain %s not yet registered"
msgstr "अधिकारक्षेत्र %s अभी पंजीकृत नहीं है"

#: registry.py:200
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "%r निर्देश पहले से अधिकार-क्षेत्र %s में पंजीकृत है, "

#: registry.py:212
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "%r भूमिका पहले से अधिकार-क्षेत्र %s में पंजीकृत है, "

#: registry.py:223
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "%r अनुक्रमणिका  पहले से अधिकार-क्षेत्र %s में पंजीकृत है"

#: registry.py:254
#, python-format
msgid "The %r object_type is already registered"
msgstr "%r object_type पहले से पंजीकृत है"

#: registry.py:280
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "%r crossref_type पहले से पंजीकृत है"

#: registry.py:287
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r पहले से पंजीकृत है"

#: registry.py:296
#, python-format
msgid "source_parser for %r is already registered"
msgstr "%r का source_parser पहले से पंजीकृत है"

#: registry.py:304
#, python-format
msgid "Source parser for %s not registered"
msgstr "%s का स्रोत व्याख्याता पंजीकृत नहीं है"

#: registry.py:320
#, python-format
msgid "Translator for %r already exists"
msgstr "%r के लिए अनुवादक पहले से विद्यमान है"

#: registry.py:336
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "add_node() के kwargs एक (visit, depart) फंक्शन टपल #function tuple# होने चाहिए: %r=%r"

#: registry.py:419
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r पहले से पंजीकृत है"

#: registry.py:431
#, python-format
msgid "math renderer %s is already registered"
msgstr ""

#: registry.py:446
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "%r आयाम को %sसंस्करण से स्फिंक्स में सम्मिलित किया जा चुका है; आयाम की उपेक्षा की गयी."

#: registry.py:457
msgid "Original exception:\n"
msgstr "मौलिक अपवाद:\n"

#: registry.py:458
#, python-format
msgid "Could not import extension %s"
msgstr "%s आयाम का आयात नहीं किया जा सका"

#: registry.py:463
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "आयाम %r में कोई सेटअप #setup()# कारक नहीं है; क्या यह वास्तव में स्फिंक्स का परिवर्धक प्रभाग है?"

#: registry.py:472
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "इस परियोजना में प्रयुक्त %s परिवर्धक को स्फिंक्स का कम से कम %s संस्करण चाहिए; इसलिए इस संस्करण से बनाना संभव नहीं है."

#: registry.py:480
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "परिवर्धक %r के सेटअप() कर्म से एक असहाय वस्तु वापस मिली है; इसको 'कुछ नहीं' अथवा मेटाडाटा कोश भेजना चाहिए था"

#: registry.py:514
#, python-format
msgid "`None` is not a valid filetype for %r."
msgstr ""

#: project.py:71
#, python-format
msgid ""
"multiple files found for the document \"%s\": %s\n"
"Use %r for the build."
msgstr ""

#: project.py:87
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: highlighting.py:168
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "पिगमेंटस लेक्सर नाम %r अज्ञात है"

#: highlighting.py:202
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "आयाम %s की needs_extensions मान में आवश्कता है, पर यह नहीं चढ़ाया गया है."

#: extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "इस परियोजना में आयाम %s का कम से कम %s संस्करण चाहिए इसलिए उपलब्ध संस्करण (%s) से बनाना संभव नहीं है."

#: theming.py:114
#, python-format
msgid ""
"Theme configuration sections other than [theme] and [options] are not "
"supported (tried to get a value from %r)."
msgstr ""

#: theming.py:120
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "विन्यास मान %s.%s खोजे गए किसी भी रूप विन्यास में नहीं दिखा"

#: theming.py:135
#, python-format
msgid "unsupported theme option %r given"
msgstr "विन्यास का असमर्थित रूप विकल्प %r दिया गया"

#: theming.py:207
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "रुपविन्यास के पथ में फाइल %r कोई प्रमाणिक ज़िप फाइल नहीं है या इसमें कोई रुपविन्यास नहीं सहेजा गया है"

#: theming.py:228
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: theming.py:268
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: theming.py:275
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: theming.py:282
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: theming.py:310
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: theming.py:335 theming.py:388
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: theming.py:339
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: theming.py:343 theming.py:391
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: theming.py:347
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: theming.py:366
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: config.py:309
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "विन्यास निर्देशिका में कोन्फ़.पाय #conf.py# फाइल (%s) नहीं है "

#: config.py:318
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr ""

#: config.py:341
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "शब्दकोष विन्यास मान %r की उल्लंघन नहीं किया जा सकता, अनदेखा किया गया (प्रत्येक अवयव का मान रखने के लिए %r का उपयोग करें)"

#: config.py:350
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "विन्यास मान %r के लिए अमान्य संख्या %r, अनदेखा किया गया"

#: config.py:356
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "असमर्थित प्रकार के साथ विन्यास मान %r का उल्लंघन नहीं किया जा सकता, अनदेखा किया गया"

#: config.py:377
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "आरोहण में अज्ञात विन्यास मान %r, अनदेखा किया गया"

#: config.py:430
#, python-format
msgid "No such config value: %r"
msgstr ""

#: config.py:453
#, python-format
msgid "Config value %r already present"
msgstr "विन्यास मान %r पहले से विद्यमान है"

#: config.py:489
#, python-format
msgid ""
"cannot cache unpickable configuration value: %r (because it contains a "
"function, class, or module object)"
msgstr ""

#: config.py:527
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "आपकी विन्यास फाइल में रचनाक्रम की त्रुटि है: %s\n"

#: config.py:530
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "विन्यास फाइल (अथवा इसके द्वारा आयातित प्रभागों) द्वारा sys.exit() का आह्वान किया गया"

#: config.py:537
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "विन्यास फाइल में प्रोग्राम के योग्य त्रुटि है:\n\n%s"

#: config.py:560
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: config.py:581 config.py:586
#, python-format
msgid "Converting `source_suffix = %r` to `source_suffix = %r`."
msgstr ""

#: config.py:589
#, python-format
msgid ""
"The config value `source_suffix' expects a dictionary, a string, or a list "
"of strings. Got `%r' instead (type %s)."
msgstr ""

#: config.py:608
#, python-format
msgid "Section %s"
msgstr "भाग %s"

#: config.py:609
#, python-format
msgid "Fig. %s"
msgstr "चित्र %s"

#: config.py:610
#, python-format
msgid "Table %s"
msgstr "सारणी %s"

#: config.py:611
#, python-format
msgid "Listing %s"
msgstr "सूची %s"

#: config.py:718
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "`{name}` विन्यास मान, {candidates} में से एक होना चाहिए, परन्तु `{current}` दिया गया है."

#: config.py:742
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "विन्यास मान `{name}' का प्रकार `{current.__name__}' है; अपेक्षित {permitted}."

#: config.py:755
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "विन्यास मान `{name}' का प्रकार `{current.__name__}' है; मानक `{default.__name__}' का प्रयोग किया गया."

#: config.py:766
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r नहीं मिला, अनदेखा किया गया."

#: config.py:778
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr ""

#: domains/rst.py:128 domains/rst.py:185
#, python-format
msgid "%s (directive)"
msgstr "%s (निर्देश)"

#: domains/rst.py:186 domains/rst.py:190
#, python-format
msgid ":%s: (directive option)"
msgstr ""

#: domains/rst.py:214
#, python-format
msgid "%s (role)"
msgstr "%s (भूमिका)"

#: domains/rst.py:224
msgid "directive"
msgstr "निर्देश"

#: domains/rst.py:225
msgid "directive-option"
msgstr ""

#: domains/rst.py:226
msgid "role"
msgstr "भूमिका"

#: domains/rst.py:248
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr ""

#: domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (अंतर्निर्मित फंक्शन)"

#: domains/javascript.py:166 domains/python/__init__.py:253
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (%s विधि)"

#: domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (वर्ग)"

#: domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (वैश्विक चरपद अथवा अचर) "

#: domains/javascript.py:172 domains/python/__init__.py:338
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (%s लक्षण)"

#: domains/javascript.py:255
msgid "Arguments"
msgstr "चर "

#: domains/cpp/__init__.py:447 domains/javascript.py:258
msgid "Throws"
msgstr "देता है "

#: domains/c/__init__.py:310 domains/cpp/__init__.py:458
#: domains/javascript.py:261 domains/python/_object.py:176
msgid "Returns"
msgstr "प्रदत्त "

#: domains/c/__init__.py:312 domains/javascript.py:263
#: domains/python/_object.py:178
msgid "Return type"
msgstr "प्रदत्त प्रकार "

#: domains/javascript.py:334
#, python-format
msgid "%s (module)"
msgstr "%s (प्रभाग)"

#: domains/c/__init__.py:681 domains/cpp/__init__.py:859
#: domains/javascript.py:371 domains/python/__init__.py:660
msgid "function"
msgstr "फंक्शन"

#: domains/javascript.py:372 domains/python/__init__.py:664
msgid "method"
msgstr "पद्धति"

#: domains/cpp/__init__.py:857 domains/javascript.py:373
#: domains/python/__init__.py:662
msgid "class"
msgstr "वर्ग"

#: domains/javascript.py:374 domains/python/__init__.py:661
msgid "data"
msgstr "आंकड़े "

#: domains/javascript.py:375 domains/python/__init__.py:667
msgid "attribute"
msgstr "लक्षण"

#: domains/javascript.py:376 domains/python/__init__.py:670
msgid "module"
msgstr "प्रभाग"

#: domains/javascript.py:407
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr ""

#: domains/changeset.py:25
#, python-format
msgid "Added in version %s"
msgstr ""

#: domains/changeset.py:26
#, python-format
msgid "Changed in version %s"
msgstr "संस्करण %s से अलग "

#: domains/changeset.py:27
#, python-format
msgid "Deprecated since version %s"
msgstr "संस्करण %s से प्रतिबंधित "

#: domains/changeset.py:28
#, python-format
msgid "Removed in version %s"
msgstr ""

#: domains/__init__.py:299
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: domains/citation.py:73
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "प्रतिरूप उद्धरण %s, दूसरी प्रतिकृति %s में है "

#: domains/citation.py:84
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "उद्धरण [%s] सन्दर्भ कहीं नहीं है"

#: domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "समीकरण का प्रतिरूप शीर्षक %s, दूसरी प्रतिकृति %s में है "

#: domains/math.py:119 writers/latex.py:2479
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "अमान्य math_eqref_format: %r"

#: environment/__init__.py:81
msgid "new config"
msgstr "नव विन्यास"

#: environment/__init__.py:82
msgid "config changed"
msgstr "विन्यास परिवर्तित"

#: environment/__init__.py:83
msgid "extensions changed"
msgstr "आयाम परिवर्तित"

#: environment/__init__.py:249
msgid "build environment version not current"
msgstr "निर्मित परिस्थिति वर्तमान संस्करण नहीं है "

#: environment/__init__.py:251
msgid "source directory has changed"
msgstr "स्रोत निर्देशिका परिवर्तित हो चुकी है "

#: environment/__init__.py:313
#, python-format
msgid "The configuration has changed (1 option: %r)"
msgstr ""

#: environment/__init__.py:318
#, python-format
msgid "The configuration has changed (%d options: %s)"
msgstr ""

#: environment/__init__.py:324
#, python-format
msgid "The configuration has changed (%d options: %s, ...)"
msgstr ""

#: environment/__init__.py:366
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "यह परिस्थिति चुने गए निर्माता से मेल नहीं खाती, कृपया दूसरी डॉक-ट्री निर्देशिका चुनें. "

#: environment/__init__.py:473
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "लेखपत्रों के पर्यवेक्षण में असफलता %s: %r"

#: environment/__init__.py:626
#, python-format
msgid "Domain %r is not registered"
msgstr "अधिकारक्षेत्र %r पंजीकृत नहीं है"

#: environment/__init__.py:777
msgid "document isn't included in any toctree"
msgstr "लेखपत्र किसी भी विषय-सूची-संरचना में सम्मिलित नहीं है"

#: environment/__init__.py:810
msgid "self referenced toctree found. Ignored."
msgstr "स्वयं-संदर्भित विषय-सूची-संरचना मिली है. उपेक्षा की गई."

#: environment/__init__.py:839
#, python-format
msgid "document is referenced in multiple toctrees: %s, selecting: %s <- %s"
msgstr ""

#: locale/__init__.py:229
msgid "Attention"
msgstr "सावधानी"

#: locale/__init__.py:230
msgid "Caution"
msgstr "चेतावनी"

#: locale/__init__.py:231
msgid "Danger"
msgstr "खतरा"

#: locale/__init__.py:232
msgid "Error"
msgstr "गलती"

#: locale/__init__.py:233
msgid "Hint"
msgstr "संकेत"

#: locale/__init__.py:234
msgid "Important"
msgstr "महत्त्वपूर्ण"

#: locale/__init__.py:235
msgid "Note"
msgstr "टिप्पणी "

#: locale/__init__.py:236
msgid "See also"
msgstr "यह भी देखिए"

#: locale/__init__.py:237
msgid "Tip"
msgstr "सलाह"

#: locale/__init__.py:238
msgid "Warning"
msgstr "चेतावनी"

#: cmd/quickstart.py:43
msgid "automatically insert docstrings from modules"
msgstr "प्रभागों में से डॉक्-स्ट्रिंग स्वतःसम्मिलित करें"

#: cmd/quickstart.py:44
msgid "automatically test code snippets in doctest blocks"
msgstr "डॉक्-टेस्ट अंशों के निर्देश भाग की स्वतः जाँच करें"

#: cmd/quickstart.py:45
msgid "link between Sphinx documentation of different projects"
msgstr "भिन्न परियोजनाओं के स्फिंक्स प्रलेखों का पारस्परिक सम्बन्ध करने दें"

#: cmd/quickstart.py:46
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "वह \"शेष\" प्रविष्टियाँ लिख लें, जिन्हें निर्माण के समय दिखाया या छिपाया जा सकता है"

#: cmd/quickstart.py:47
msgid "checks for documentation coverage"
msgstr "प्रलेखों की व्याप्ति की जाँच करें"

#: cmd/quickstart.py:48
msgid "include math, rendered as PNG or SVG images"
msgstr "गणित को सम्मिलित करें, पी.एन.जी. अथवा एस.वी.जी. में चित्रित"

#: cmd/quickstart.py:49
msgid "include math, rendered in the browser by MathJax"
msgstr "गणित को सम्मिलित करें, दिग्दर्शक में मैथजाक्स #MathJax# द्वारा प्रदर्शित"

#: cmd/quickstart.py:50
msgid "conditional inclusion of content based on config values"
msgstr "विन्यास मान के आधार पर सामिग्री का सशर्त समावेश"

#: cmd/quickstart.py:51
msgid "include links to the source code of documented Python objects"
msgstr "पाइथन विषयवस्तुओं के प्रलेखों के स्रोत निर्देश की कड़ी जोड़ें"

#: cmd/quickstart.py:52
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "गिटहब GitHub पर लेखपत्र प्रकाशित करने के लिए .nojekyll फाइल बनाएं"

#: cmd/quickstart.py:94
msgid "Please enter a valid path name."
msgstr "कृपया एक मान्य पथ का नाम दें"

#: cmd/quickstart.py:110
msgid "Please enter some text."
msgstr "कृपया कुछ वाक्यांश लिखें"

#: cmd/quickstart.py:117
#, python-format
msgid "Please enter one of %s."
msgstr "%s में से एक चुनें"

#: cmd/quickstart.py:125
msgid "Please enter either 'y' or 'n'."
msgstr "कृपया हाँ के लिए 'y' अथवा नहीं के लिए 'n' मात्र दें. "

#: cmd/quickstart.py:131
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "कृपया एक फाइल प्रत्यय दें, जैसे कि '.rst' अथवा '.txt'."

#: cmd/quickstart.py:215
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "स्फिंक्स %s त्वरित-आरंभ #sphinx-quickstart# उपकरण के लिए अभिनन्दन"

#: cmd/quickstart.py:219
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "कृपया निम्न विन्यासों के लिए मान प्रदान करें (मानक मान, यदि कोष्ठक में हो तो, स्वीकार करने के लिए एन्टर दबाएँ)"

#: cmd/quickstart.py:227
#, python-format
msgid "Selected root path: %s"
msgstr "चुना हुआ बुनियादी तथा मूल स्थान: %s"

#: cmd/quickstart.py:230
msgid "Enter the root path for documentation."
msgstr "आलेख का बुनियादी स्थान बताएं."

#: cmd/quickstart.py:231
msgid "Root path for the documentation"
msgstr "आलेख का बुनियादी पथ"

#: cmd/quickstart.py:239
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "त्रुटि: एक मौजूदा conf.py फाइल दिए गए मूल पथ में प्राप्त हुई है."

#: cmd/quickstart.py:245
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "स्फिंक्स-त्वरित-आरम्भ #sphinx-quickstart# मौजूदा स्फिंक्स परियोजनाओं पर पुनर्लेखन नहीं करेगा."

#: cmd/quickstart.py:248
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "कृपया एक नया मूल पथ दें (अथवा निकलने हेतु सिर्फ एन्टर #Enter# कर दें)"

#: cmd/quickstart.py:258
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "आपके पास Sphinx द्वारा बनाई गई फाइलों को सहेजने के लिए दो विकल्प हैं.\nया तो आप मूल स्थान में ही \"_build\" निर्देशिका प्रयोग करें, अथवा\nमूल पथ में भिन्न \"स्रोत\" और \"build\" निर्देशिका प्रयोग करें."

#: cmd/quickstart.py:265
msgid "Separate source and build directories (y/n)"
msgstr "विभिन्न स्रोत और निर्माण डायरेक्टरी (y/n)"

#: cmd/quickstart.py:271
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "मूल निर्देशिका के अन्दर, दो और निर्देशिका बनाई जाएँगी;\nपरिवर्धित एच.टी.एम्.एल. नमूनों के लिए \"_templates\" और परिवर्धित रुपपत्रों और अन्य स्थैतिक फाइलों के लिए \"_static\"\nआप अधोरेखा के स्थान पर अन्य पूर्व-प्रत्यय (जैसे कि \".\") का प्रयोग कर सकते हैं."

#: cmd/quickstart.py:277
msgid "Name prefix for templates and static dir"
msgstr "नमूने और स्थैतिक डायरेक्टरी के लिए पूर्व-प्रत्यय"

#: cmd/quickstart.py:282
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "परियोजना का नाम बनाये गए प्रपत्रों में बहुत से स्थानों पर प्रयुक्त होगा."

#: cmd/quickstart.py:286
msgid "Project name"
msgstr "परियोजना का नाम"

#: cmd/quickstart.py:288
msgid "Author name(s)"
msgstr "लेखक(कों) का नाम"

#: cmd/quickstart.py:293
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr ""

#: cmd/quickstart.py:301
msgid "Project version"
msgstr "परियोजना संस्करण"

#: cmd/quickstart.py:303
msgid "Project release"
msgstr "परियोजना आवृत्ति"

#: cmd/quickstart.py:308
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "यदि प्रलेखों को अंग्रेजी के अलावा अन्य किसी भाषा में लिखा जाना है,\nतो यहाँ पर आप भाषा का कूटशब्द दे सकते हैं. स्फिंक्स तदपुरांत,\nजो वाक्यांश बनाता है उसे उस भाषा में अनुवादित करेगा.\n\nमान्य भाषा कूटशब्द सूची यहाँ पर देखें\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."

#: cmd/quickstart.py:317
msgid "Project language"
msgstr "परियोजना की भाषा"

#: cmd/quickstart.py:324
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr ""

#: cmd/quickstart.py:329
msgid "Source file suffix"
msgstr "स्रोत फाइल का प्रत्यय"

#: cmd/quickstart.py:334
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr ""

#: cmd/quickstart.py:342
msgid "Name of your master document (without suffix)"
msgstr "आपने मुख्य लेखपत्र का नाम दें (प्रत्यय रहित)"

#: cmd/quickstart.py:352
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "त्रुटि: मुख्य फाइल %s चुने हुए मूल पथ में पहले से उपलब्ध है."

#: cmd/quickstart.py:359
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "स्फिंक्स-त्वरित-आरम्भ मौजूदा फाइलों पर पुनर्लेखन नहीं करेगा."

#: cmd/quickstart.py:362
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "कृपया एक नया फाइल नाम दें, अथवा मौजूदा फाइल का पुनर्नामकरण करें और एन्टर दबाएँ"

#: cmd/quickstart.py:371
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "इनमें से कौन सा स्फिंक्स आयाम प्रयोग करना है, इंगित करें:"

#: cmd/quickstart.py:381
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "टिप्पणी: imgmath और mathjax एक साथ समर्थ नहीं हो सकते. imgmath को अचिन्हित कर दिया गया है."

#: cmd/quickstart.py:391
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr ""

#: cmd/quickstart.py:397
msgid "Create Makefile? (y/n)"
msgstr "मेकफाइल बनाएं? (हाँ के लिए y/ ना के लिए n)"

#: cmd/quickstart.py:401
msgid "Create Windows command file? (y/n)"
msgstr "विंडोज़ कमांड फाइल बनाएं? (हाँ के लिए y/ ना के लिए n)"

#: cmd/quickstart.py:453 ext/apidoc.py:92
#, python-format
msgid "Creating file %s."
msgstr "फाइल बनाई जा रही है ...%s"

#: cmd/quickstart.py:458 ext/apidoc.py:89
#, python-format
msgid "File %s already exists, skipping."
msgstr "फाइल %s पहले से उपस्थित है, छोड़ दी गई."

#: cmd/quickstart.py:501
msgid "Finished: An initial directory structure has been created."
msgstr "समाप्त: एक प्रारंभिक निर्देशिका का ढांचा बना दिया गया है."

#: cmd/quickstart.py:504
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr ""

#: cmd/quickstart.py:512
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr ""

#: cmd/quickstart.py:515
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr ""

#: cmd/quickstart.py:522
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr ""

#: cmd/quickstart.py:557
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nस्फिंक्स परियोजना के लिए आवश्यक फाइल बनाएं.\n\nस्फिंक्स-त्वरित-आरम्भ एक संवादपूर्ण उपकरण है जो आपकी परियोजना के \nबारे में कुछ प्रश्न पूछकर पूरी प्रलेखों की निर्देशिका और नमूना मेकफाइल \nबना देता है जिसे स्फिंक्स-बिल्ड में प्रयोग किया जा सकता है.\n"

#: cmd/build.py:153 cmd/quickstart.py:567 ext/apidoc.py:374
#: ext/autosummary/generate.py:766
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr ""

#: cmd/quickstart.py:577
msgid "quiet mode"
msgstr "शांत ढंग "

#: cmd/quickstart.py:587
msgid "project root"
msgstr ""

#: cmd/quickstart.py:590
msgid "Structure options"
msgstr "ढांचे के विकल्प"

#: cmd/quickstart.py:596
msgid "if specified, separate source and build dirs"
msgstr "यदि निर्दिष्ट हो तो विभिन्न स्रोत और निर्माण पथ"

#: cmd/quickstart.py:602
msgid "if specified, create build dir under source dir"
msgstr ""

#: cmd/quickstart.py:608
msgid "replacement for dot in _templates etc."
msgstr "_templates आदि में बिंदु का बदलाव"

#: cmd/quickstart.py:611
msgid "Project basic options"
msgstr "परोयोजना के मूलभूत विकल्प"

#: cmd/quickstart.py:613
msgid "project name"
msgstr "परियोजना का नाम"

#: cmd/quickstart.py:616
msgid "author names"
msgstr "लेखकों के नाम"

#: cmd/quickstart.py:623
msgid "version of project"
msgstr "परियोजना का संस्करण"

#: cmd/quickstart.py:630
msgid "release of project"
msgstr "परियोजना की आवृत्ति"

#: cmd/quickstart.py:637
msgid "document language"
msgstr "लेखपत्र की भाषा"

#: cmd/quickstart.py:640
msgid "source file suffix"
msgstr "स्रोत फाइल का प्रत्यय"

#: cmd/quickstart.py:643
msgid "master document name"
msgstr "मुख्य लेखपत्र का नाम"

#: cmd/quickstart.py:646
msgid "use epub"
msgstr "ई-पब प्रयोग करें"

#: cmd/quickstart.py:649
msgid "Extension options"
msgstr "आयाम के विकल्प"

#: cmd/quickstart.py:656 ext/apidoc.py:578
#, python-format
msgid "enable %s extension"
msgstr "आयाम %s सक्षम करें"

#: cmd/quickstart.py:663 ext/apidoc.py:570
msgid "enable arbitrary extensions"
msgstr "स्वेच्छित आयाम सक्षम करें"

#: cmd/quickstart.py:666
msgid "Makefile and Batchfile creation"
msgstr "मेकफाइल और बैचफाइल का सर्जन"

#: cmd/quickstart.py:672
msgid "create makefile"
msgstr "मेकफाइल बनाएं"

#: cmd/quickstart.py:678
msgid "do not create makefile"
msgstr "मेकफाइल नहीं बनाएं"

#: cmd/quickstart.py:685
msgid "create batchfile"
msgstr "बैचफाइल बनाएं"

#: cmd/quickstart.py:691
msgid "do not create batchfile"
msgstr "बैचफाइल नहीं बनाएं"

#: cmd/quickstart.py:700
msgid "use make-mode for Makefile/make.bat"
msgstr "Makefile/make.bat के लिए make-mode का प्रयोग करें"

#: cmd/quickstart.py:703 ext/apidoc.py:581
msgid "Project templating"
msgstr "परियोजना नमूनावृत्ति"

#: cmd/quickstart.py:709 ext/apidoc.py:587
msgid "template directory for template files"
msgstr "नमूना फाइलों के लिए नमूना निर्देशिका"

#: cmd/quickstart.py:716
msgid "define a template variable"
msgstr "नमूना चर-पद का निरूपण करें"

#: cmd/quickstart.py:751
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "\"शांत\" निर्दिष्ट है, परन्तु कोई भी \"परियोजना\" अथवा \"लेखक\" निर्दिष्ट नहीं है."

#: cmd/quickstart.py:770
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "त्रुटि: दिया गया पथ निर्देशिका नहीं है, अथवा स्फिंक्स फाइलें पहले से उपस्थित हैं."

#: cmd/quickstart.py:777
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "स्फिंक्स-त्वरित-आरम्भ केवल एक खाली निर्देशिका में कार्यशील हो सकती है. कृपया एक नया मूल पथ निर्दिष्ट करें."

#: cmd/quickstart.py:795
#, python-format
msgid "Invalid template variable: %s"
msgstr "अमान्य नमूना चर-पद: %s"

#: cmd/build.py:49
msgid "Exception occurred while building, starting debugger:"
msgstr "निर्माण के दौरान अपवाद घटित हुआ है, दोष-मुक्तक चालू किया जा रहा "

#: _cli/util/errors.py:129 cmd/build.py:65
msgid "Interrupted!"
msgstr "कार्य खंडित "

#: cmd/build.py:67
msgid "reST markup error:"
msgstr "रेस्ट सुसज्जा त्रुटि:"

#: _cli/util/errors.py:143 cmd/build.py:73
msgid "Encoding error:"
msgstr "कूटलेखन त्रुटि:"

#: cmd/build.py:78 cmd/build.py:108
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr "यदि आप इस विषय को कूटलिपिकारों के संज्ञान में लाना चाहते है तो   पिछला पूरा विवरण %s में सहेज दिया गया है"

#: _cli/util/errors.py:148 cmd/build.py:90
msgid "Recursion error:"
msgstr "पुनरावर्तन त्रुटि:"

#: _cli/util/errors.py:152 cmd/build.py:94
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr ""

#: _cli/util/errors.py:165 cmd/build.py:103
msgid "Exception occurred:"
msgstr "अपवाद घटित:"

#: _cli/util/errors.py:178 cmd/build.py:117
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "यदि यह प्रयोक्ता की गलती थी तो कृपया इसको भी रिपोर्ट करें ताकि अगली बार गलती होने पर अधिक अर्थपूर्ण सन्देश दिया जा सके."

#: cmd/build.py:124
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "त्रुटि की सूचना <https://github.com/sphinx-doc/sphinx/issues> पर उपस्थित पंजिका में दर्ज की जा सकती है. धन्यवाद!"

#: cmd/build.py:144
msgid "job number should be a positive number"
msgstr "कार्य संख्या एक धनात्मक संख्या होनी चाहिए"

#: cmd/build.py:154
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr ""

#: cmd/build.py:180
msgid "path to documentation source files"
msgstr "अभिलेख की स्रोत फाइलों का पथ"

#: cmd/build.py:183
msgid "path to output directory"
msgstr "परिणाम निर्देशिका का पथ"

#: cmd/build.py:188
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: cmd/build.py:194
msgid "general options"
msgstr "सामान्य विकल्प"

#: cmd/build.py:201
msgid "builder to use (default: 'html')"
msgstr ""

#: cmd/build.py:210
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: cmd/build.py:220
msgid "write all files (default: only write new and changed files)"
msgstr "सभी फाइलें लिखें (मानक: केवल नई और परिवर्तित फाइलें लिखें)"

#: cmd/build.py:227
msgid "don't use a saved environment, always read all files"
msgstr "सहेजी गयी परिस्थिति का प्रयोग न करें, सदैव सभी फाइलों को पढ़ें"

#: cmd/build.py:230
msgid "path options"
msgstr ""

#: cmd/build.py:236
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: cmd/build.py:246
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: cmd/build.py:255
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: cmd/build.py:264
msgid "override a setting in configuration file"
msgstr "विन्यास फाइल के एक मान का उल्लंघन करें "

#: cmd/build.py:273
msgid "pass a value into HTML templates"
msgstr "एच.टी.एम्.एल. के नमूने में राशि प्रेषित करें"

#: cmd/build.py:282
msgid "define tag: include \"only\" blocks with TAG"
msgstr "नाम-पत्र परिभाषित करें: केवल नाम-पत्र वाले खण्डों का समावेश करें"

#: cmd/build.py:289
msgid "nitpicky mode: warn about all missing references"
msgstr ""

#: cmd/build.py:292
msgid "console output options"
msgstr "प्रदर्शित परिणामों के विकल्प"

#: cmd/build.py:299
msgid "increase verbosity (can be repeated)"
msgstr "शब्द-प्रयोग बढ़ाएं (पुनरावृत्ति की जा सकती है) "

#: cmd/build.py:306 ext/apidoc.py:413
msgid "no output on stdout, just warnings on stderr"
msgstr "एस.टी.डी आउट #stdout# पर कोई परिणाम नहीं, एस.टी.डी एरर #stderr# पर चेतावनियाँ "

#: cmd/build.py:313
msgid "no output at all, not even warnings"
msgstr "कुछ भी निर्गमित नहीं, यहाँ तक कि चेतावनी भी नहीं"

#: cmd/build.py:321
msgid "do emit colored output (default: auto-detect)"
msgstr "रंगीन परिणाम ही दिखाएँ (मानक: स्वतः अनुमानित)"

#: cmd/build.py:329
msgid "do not emit colored output (default: auto-detect)"
msgstr "रंगीन परिणाम नहीं दिखाएँ (मानक: स्वतः अनुमानित)"

#: cmd/build.py:332
msgid "warning control options"
msgstr ""

#: cmd/build.py:338
msgid "write warnings (and errors) to given file"
msgstr "चेतावनियाँ (और त्रुटियाँ) दी गई फाइल में लिखें"

#: cmd/build.py:345
msgid "turn warnings into errors"
msgstr "चेतावनियों को अशुद्धि मानें"

#: cmd/build.py:353
msgid "show full traceback on exception"
msgstr "अपवाद होने पर पूरा विलोम-अनुगमन देखें"

#: cmd/build.py:356
msgid "run Pdb on exception"
msgstr "अपवाद होने पर पी.डी.बी. चलाएं"

#: cmd/build.py:362
msgid "raise an exception on warnings"
msgstr ""

#: cmd/build.py:405
msgid "cannot combine -a option and filenames"
msgstr "-a विकल्प और फाइल के नामों को सम्मिलित नहीं किया जा सकता"

#: cmd/build.py:437
#, python-format
msgid "cannot open warning file '%s': %s"
msgstr ""

#: cmd/build.py:456
msgid "-D option argument must be in the form name=value"
msgstr "-D विकल्प का मान नाम = मान के रूप में होना आवश्यक है"

#: cmd/build.py:463
msgid "-A option argument must be in the form name=value"
msgstr "-A विकल्प का मान नाम = मान के रूप में होना आवश्यक है"

#: builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "मूक निर्माता से किसी फाइलों की उत्पत्ति नहीं होती."

#: builders/linkcheck.py:75
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "उपरोक्त परिणाम में अथवा %(outdir)s /output.txt में त्रुटियाँ ढूँढने का प्रयास "

#: builders/linkcheck.py:146
#, python-format
msgid "broken link: %s (%s)"
msgstr "खंडित कड़ी: %s (%s)"

#: builders/linkcheck.py:540
#, python-format
msgid "Anchor '%s' not found"
msgstr "लक्ष्य '%s' नहीं मिला"

#: builders/linkcheck.py:742
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr ""

#: builders/singlehtml.py:37
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "एच.टी.एम्.एल. पृष्ठ %(outdir)sमें है."

#: builders/singlehtml.py:173
msgid "assembling single document"
msgstr "एकल लेखपत्र संकलन किया जा रहा है"

#: builders/latex/__init__.py:346 builders/manpage.py:56
#: builders/singlehtml.py:178 builders/texinfo.py:121
msgid "writing"
msgstr "लिखा जा रहा है"

#: builders/singlehtml.py:191
msgid "writing additional files"
msgstr "अतिरिक्त फाइलों को लिखा जा रहा है"

#: builders/manpage.py:39
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "पुस्तिका पृष्ठ %(outdir)sमें हैं."

#: builders/manpage.py:47
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "कोई \"man_pages\" विन्यास मान नहीं मिला; कोई नियमावली पृष्ठ नहीं लिखे जाएंगे"

#: builders/manpage.py:73
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "\"man_pages\" विन्यास मान अज्ञात लेखपत्र %s का सन्दर्भ है"

#: builders/text.py:34
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "पाठ फाइल %(outdir)s में हैं."

#: builders/html/__init__.py:1239 builders/text.py:81 builders/xml.py:97
#, python-format
msgid "error writing file %s: %s"
msgstr "%s फाइल लिखने में व्यवधान: %s"

#: builders/xml.py:38
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "एक्स.एम्.एल. लेखपत्र %(outdir)s में हैं."

#: builders/xml.py:110
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "छद्म-एक्स.एम्.एल. लेखपत्र %(outdir)s में हैं."

#: builders/texinfo.py:47
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "टेक्सइन्फो पृष्ठ %(outdir)sमें हैं."

#: builders/texinfo.py:49
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nइन्हें मेकइन्फो से चलाने के लिए उस निर्देशिका में 'मेक' आदेश चलायें\n(ऐसा स्वचालित रूप से करने के लिए यहाँ 'मेक इन्फो' आदेश का उपयोग करें)"

#: builders/texinfo.py:78
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "कोई \"texinfo_documents\" विन्यास मान नहीं मिला; कोई लेखपत्र नहीं लिखे जाएंगे"

#: builders/texinfo.py:90
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "\"texinfo_documents\" विन्यास मान अज्ञात लेखपत्र %s का सन्दर्भ है"

#: builders/latex/__init__.py:324 builders/texinfo.py:115
#, python-format
msgid "processing %s"
msgstr "%s की प्रक्रिया जारी"

#: builders/latex/__init__.py:404 builders/texinfo.py:174
msgid "resolving references..."
msgstr "सन्दर्भों का विश्लेषण किया जा रहा है..."

#: builders/latex/__init__.py:415 builders/texinfo.py:184
msgid " (in "
msgstr " (में"

#: builders/_epub_base.py:423 builders/html/__init__.py:778
#: builders/latex/__init__.py:482 builders/texinfo.py:202
msgid "copying images... "
msgstr "चित्रों की प्रतिलिपि बनाई जा रही है..."

#: builders/_epub_base.py:445 builders/latex/__init__.py:497
#: builders/texinfo.py:219
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "चित्रलेख फाइल %r की प्रतिलिपि नहीं की जा सकी:%s"

#: builders/texinfo.py:226
msgid "copying Texinfo support files"
msgstr "टेक्सइन्फो सहायक फाइलों की प्रतिलिपि की जा रही है..."

#: builders/texinfo.py:234
#, python-format
msgid "error writing file Makefile: %s"
msgstr "मेकफाइल लिखने में त्रुटि: %s"

#: builders/gettext.py:230
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "सन्देश सूचीपत्र %(outdir)s में हैं."

#: builders/__init__.py:383 builders/gettext.py:251
#, python-format
msgid "building [%s]: "
msgstr "निर्माणाधीन [%s]: "

#: builders/gettext.py:252
#, python-format
msgid "targets for %d template files"
msgstr "%d नमूना फाइलों के लक्ष्य"

#: builders/gettext.py:257
msgid "reading templates... "
msgstr "नमूनों को पढ़ा जा रहा है..."

#: builders/gettext.py:292
msgid "writing message catalogs... "
msgstr "सन्देश सूचीपत्रों को लिखा जा रहा है..."

#: builders/__init__.py:212
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "%s निर्माता के लिए योग्य चित्र नहीं मिला: %s.(%s)"

#: builders/__init__.py:220
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "%s निर्माता के लिए योग्य चित्र नहीं मिला: %s"

#: builders/__init__.py:243
msgid "building [mo]: "
msgstr "निर्माणाधीन [mo]: "

#: builders/__init__.py:246 builders/__init__.py:741 builders/__init__.py:773
msgid "writing output... "
msgstr "परिणाम लिखा जा रहा है..."

#: builders/__init__.py:263
#, python-format
msgid "all of %d po files"
msgstr "सभी %d पी.ओ. फाइलें"

#: builders/__init__.py:285
#, python-format
msgid "targets for %d po files that are specified"
msgstr "निर्दिष्ट %d पी.ओ. फाइलों के लक्ष्य"

#: builders/__init__.py:297
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "%d पी.ओ. फाइलों के लक्ष्य कालातीत है"

#: builders/__init__.py:307
msgid "all source files"
msgstr "सभी स्रोत फाइलें"

#: builders/__init__.py:319
#, python-format
msgid "file %r given on command line does not exist, "
msgstr ""

#: builders/__init__.py:325
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "आदेश स्थान में दी गयी फाइल %r स्रोत निर्देशिका में नहीं है, उपेक्षा की जा रही है"

#: builders/__init__.py:336
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr ""

#: builders/__init__.py:351
#, python-format
msgid "%d source files given on command line"
msgstr "%d स्रोत फाइलें आदेश स्थान में दी "

#: builders/__init__.py:366
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "%d  फाइलों के लक्ष्य कालातीत है"

#: builders/__init__.py:394
msgid "looking for now-outdated files... "
msgstr "अप्रचलित फाइलों को चिन्हित किया जा रहा है..."

#: builders/__init__.py:398
#, python-format
msgid "%d found"
msgstr "%d मिला"

#: builders/__init__.py:400
msgid "none found"
msgstr "एक भी नहीं मिला"

#: builders/__init__.py:407
msgid "pickling environment"
msgstr "स्थिति को परिरक्षित किया जा रहा है"

#: builders/__init__.py:414
msgid "checking consistency"
msgstr "संगतता की जांच की जा रही है"

#: builders/__init__.py:418
msgid "no targets are out of date."
msgstr "कोई प्रयोजन कालातीत नहीं है"

#: builders/__init__.py:458
msgid "updating environment: "
msgstr "स्थिति का नवीनीकरण किया जा रहा है"

#: builders/__init__.py:483
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s जोड़ा गया, %s बदला गया, %s हटाया गया"

#: builders/__init__.py:519
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches a "
"built-in exclude pattern %r. Please move your master document to a different"
" location."
msgstr ""

#: builders/__init__.py:528
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches an "
"exclude pattern specified in conf.py, %r. Please remove this pattern from "
"conf.py."
msgstr ""

#: builders/__init__.py:539
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it is not included"
" in the custom include_patterns = %r. Ensure that a pattern in "
"include_patterns matches the master document."
msgstr ""

#: builders/__init__.py:546
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s). The master document must "
"be within the source directory or a subdirectory of it."
msgstr ""

#: builders/__init__.py:565 builders/__init__.py:581
msgid "reading sources... "
msgstr "स्रोतों को पढ़ा जा रहा है..."

#: builders/__init__.py:698
#, python-format
msgid "docnames to write: %s"
msgstr "लेखन के लिए शेष लेखपत्र: %s"

#: builders/__init__.py:711
msgid "preparing documents"
msgstr "लेखपत्र बनाए जा रहे हैं"

#: builders/__init__.py:714
msgid "copying assets"
msgstr ""

#: builders/__init__.py:866
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "असाधनीय स्रोत अक्षर, \"?\" द्वारा बदले जा रहे हैं: %r"

#: builders/epub3.py:83
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "ई-पब फाइल %(outdir)s में है."

#: builders/epub3.py:189
msgid "writing nav.xhtml file..."
msgstr ""

#: builders/epub3.py:220
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_language\" (अथवा \"language\") खाली नहीं होना चाहिए"

#: builders/epub3.py:227
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_uid\" एक्स.एम्.एल. नाम होना चाहिए"

#: builders/epub3.py:231
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_title\" (अथवा \"html_title\") खाली नहीं होना चाहिए"

#: builders/epub3.py:238
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_author\" खाली नहीं होना चाहिए"

#: builders/epub3.py:242
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_contributor\" खाली नहीं होना चाहिए"

#: builders/epub3.py:247
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_description\" खाली नहीं होना चाहिए"

#: builders/epub3.py:251
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_publisher\" खाली नहीं होना चाहिए"

#: builders/epub3.py:255
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_copyright\" (अथवा \"copyright\") खाली नहीं होना चाहिए"

#: builders/epub3.py:262
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"epub_identifier\" खाली नहीं होना चाहिए"

#: builders/epub3.py:265
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "ई-पब3 के लिए विन्यास मान \"version\" खाली नहीं होना चाहिए"

#: builders/epub3.py:279 builders/html/__init__.py:1289
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "अमान्य css_file: %r, उपेक्षित"

#: builders/_epub_base.py:222
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "विषय-सूची प्रविष्टि की प्रतिलिपि पायी गई: %s"

#: builders/_epub_base.py:434
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "चित्रलेख फाइल %r नहीं पढ़ा जा सका: इसकी प्रतिलिपि बनाई जा रही है"

#: builders/_epub_base.py:465
#, python-format
msgid "cannot write image file %r: %s"
msgstr "चित्रलेख फाइल %r नहीं लिखा जा सका:%s"

#: builders/_epub_base.py:477
msgid "Pillow not found - copying image files"
msgstr "पिलो नहीं मिला - चित्र फाइलों की प्रतिलिपि बनाई जा रही है"

#: builders/_epub_base.py:512
msgid "writing mimetype file..."
msgstr ""

#: builders/_epub_base.py:521
msgid "writing META-INF/container.xml file..."
msgstr ""

#: builders/_epub_base.py:558
msgid "writing content.opf file..."
msgstr ""

#: builders/_epub_base.py:590
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "%s के लिए अज्ञात लेख प्रकार, छोड़ा गया"

#: builders/_epub_base.py:745
msgid "node has an invalid level"
msgstr ""

#: builders/_epub_base.py:764
msgid "writing toc.ncx file..."
msgstr ""

#: builders/_epub_base.py:793
#, python-format
msgid "writing %s file..."
msgstr "%s फाइल को लिखा जा रहा है..."

#: builders/changes.py:33
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "संक्षिप्त विवरण फाइल %(outdir)s में है."

#: builders/changes.py:60
#, python-format
msgid "no changes in version %s."
msgstr "%s संस्करण में कोई परिवर्तन नहीं हैं."

#: builders/changes.py:62
msgid "writing summary file..."
msgstr "सार फाइल को लिखा जा रहा है..."

#: builders/changes.py:74
msgid "Builtins"
msgstr "अंतर्निर्मित"

#: builders/changes.py:76
msgid "Module level"
msgstr "प्रभाग स्तर"

#: builders/changes.py:128
msgid "copying source files..."
msgstr "स्रोत फाइलों की प्रतिलिपि बनाई जा रही है..."

#: builders/changes.py:137
#, python-format
msgid "could not read %r for changelog creation"
msgstr "परिवर्तन सूची बनाने के लिए %r को नहीं पढ़ा जा सका"

#: util/rst.py:72
#, python-format
msgid "default role %s not found"
msgstr "मानक भूमिका '%s' नहीं मिली"

#: util/docfields.py:95
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr ""

#: util/osutil.py:130
#, python-format
msgid ""
"Aborted attempted copy from %s to %s (the destination path has existing "
"data)."
msgstr ""

#: util/nodes.py:419
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: util/nodes.py:487
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "विषय-सूची-संरचना में अविद्यमान फाइल %r का सन्दर्भ है"

#: util/nodes.py:701
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "केवल निर्देशक भाव का मूल्यांकन करते समय अपवाद: %s"

#: util/fileutil.py:74
#, python-format
msgid ""
"Aborted attempted copy from rendered template %s to %s (the destination path"
" has existing data)."
msgstr ""

#: util/fileutil.py:89
#, python-format
msgid "Writing evaluated template result to %s"
msgstr ""

#: util/inventory.py:170
#, python-format
msgid "inventory <%s> contains duplicate definitions of %s"
msgstr ""

#: util/inventory.py:185
#, python-format
msgid "inventory <%s> contains multiple definitions for %s"
msgstr ""

#: util/docutils.py:284
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr ""

#: util/docutils.py:747
#, python-format
msgid "unknown node type: %r"
msgstr "अज्ञात बिंदु प्रकार: %r"

#: util/display.py:81
msgid "skipped"
msgstr "छोड़ा "

#: util/display.py:86
msgid "failed"
msgstr "असफल"

#: util/i18n.py:103
#, python-format
msgid "reading error: %s, %s"
msgstr "अशुद्धि पाठन: %s, %s"

#: util/i18n.py:110
#, python-format
msgid "writing error: %s, %s"
msgstr "अशुद्धि लेखन: %s, %s"

#: util/i18n.py:138
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: util/i18n.py:230
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "अमान्य तिथि प्रारूप. यदि आप सीधे परिणाम में दर्शाना चाहते हैं तो अक्षरमाला को एकाकी उद्धरण चिन्ह द्वारा चिन्हित करें: %s"

#: directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr ""

#: directives/code.py:66
msgid "non-whitespace stripped by dedent"
msgstr ""

#: directives/code.py:87
#, python-format
msgid "Invalid caption: %s"
msgstr "अमान्य शीर्षक: %s"

#: directives/code.py:132 directives/code.py:297 directives/code.py:484
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "पंक्ति संख्या का ब्यौरा सीमा से बाहर है (1-%d): %r"

#: directives/code.py:216
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "दोनों \"%s\" और \"%s\" विकल्पों का प्रयोग नहीं किया जा सकता"

#: directives/code.py:231
#, python-format
msgid "Include file '%s' not found or reading it failed"
msgstr ""

#: directives/code.py:234
#, python-format
msgid ""
"Encoding %r used for reading included file '%s' seems to be wrong, try "
"giving an :encoding: option"
msgstr ""

#: directives/code.py:276
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "%r नामक विषयवस्तु सम्मिलित फाइल %r में नहीं मिली"

#: directives/code.py:309
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "\"lineno-match\" का प्रयोग बिना जुडी \"lines\" के युग्म के साथ नहीं हो सकता"

#: directives/code.py:314
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "लाइन ब्यौरा %r: सम्मिलित फाइल %r से कोई लाइन नहीं ली जा सकीं"

#: directives/other.py:122
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr ""

#: directives/other.py:155 environment/adapters/toctree.py:355
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "विषय-सूची-संरचना में छोड़े गए लेखपत्र %r का सन्दर्भ है"

#: directives/other.py:158 environment/adapters/toctree.py:359
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "विषय-सूची-संरचना में अविद्यमान लेखपत्र %r का सन्दर्भ है"

#: directives/other.py:171
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr ""

#: directives/other.py:204
msgid "Section author: "
msgstr "भाग के लेखक:"

#: directives/other.py:206
msgid "Module author: "
msgstr "प्रभाग लेखक:"

#: directives/other.py:208
msgid "Code author: "
msgstr "निर्देश लेखक:"

#: directives/other.py:210
msgid "Author: "
msgstr "लेखक:"

#: directives/other.py:284
msgid ".. acks content is not a list"
msgstr ""

#: directives/other.py:309
msgid ".. hlist content is not a list"
msgstr ""

#: _cli/__init__.py:73
msgid "Usage:"
msgstr ""

#: _cli/__init__.py:75
msgid "{0} [OPTIONS] <COMMAND> [<ARGS>]"
msgstr ""

#: _cli/__init__.py:78
msgid "  The Sphinx documentation generator."
msgstr ""

#: _cli/__init__.py:87
msgid "Commands:"
msgstr ""

#: _cli/__init__.py:98
msgid "Options"
msgstr ""

#: _cli/__init__.py:112 _cli/__init__.py:183
msgid "For more information, visit https://www.sphinx-doc.org/en/master/man/."
msgstr ""

#: _cli/__init__.py:172
msgid ""
"{0}: error: {1}\n"
"Run '{0} --help' for information"
msgstr ""

#: _cli/__init__.py:182
msgid "   Manage documentation with Sphinx."
msgstr ""

#: _cli/__init__.py:194
msgid "Show the version and exit."
msgstr ""

#: _cli/__init__.py:202
msgid "Show this message and exit."
msgstr ""

#: _cli/__init__.py:206
msgid "Logging"
msgstr ""

#: _cli/__init__.py:213
msgid "Increase verbosity (can be repeated)"
msgstr ""

#: _cli/__init__.py:221
msgid "Only print errors and warnings."
msgstr ""

#: _cli/__init__.py:228
msgid "No output at all"
msgstr ""

#: _cli/__init__.py:234
msgid "<command>"
msgstr ""

#: _cli/__init__.py:265
msgid "See 'sphinx --help'.\n"
msgstr ""

#: builders/html/__init__.py:486 builders/latex/__init__.py:198
#: transforms/__init__.py:133 writers/manpage.py:102 writers/texinfo.py:219
#, python-format
msgid "%b %d, %Y"
msgstr "%b %d, %Y"

#: transforms/__init__.py:143
msgid "could not calculate translation progress!"
msgstr ""

#: transforms/__init__.py:148
msgid "no translated elements!"
msgstr ""

#: transforms/__init__.py:267
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "4 पंक्तिबद्ध सूचियाँ मिलीं. यह आपके द्वारा उपयोग किए गए आयाम की त्रुटि हो सकती है: %r"

#: transforms/__init__.py:313
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "पाद-टिप्पणी [%s] का कोई सन्दर्भ नहीं है."

#: transforms/__init__.py:322
msgid "Footnote [*] is not referenced."
msgstr ""

#: transforms/__init__.py:333
msgid "Footnote [#] is not referenced."
msgstr "पाद-टिप्पणी [#] सन्दर्भ कहीं नहीं है"

#: transforms/i18n.py:228 transforms/i18n.py:303
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "अनुवादित संदेश में असंगत पाद-टिप्पणी के प्रसंग. मूल: {0}, अनुवादित: {1}"

#: transforms/i18n.py:273
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "अनुवादित संदेश में असंगत प्रसंग. मूल: {0}, अनुवादित: {1}"

#: transforms/i18n.py:323
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "अनुवादित संदेश में असंगत उद्धरण के प्रसंग. मूल: {0}, अनुवादित: {1}"

#: transforms/i18n.py:345
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "अनुवादित संदेश में असंगत शब्द के प्रसंग. मूल: {0}, अनुवादित: {1}"

#: ext/linkcode.py:75 ext/viewcode.py:201
msgid "[source]"
msgstr "[स्रोत]"

#: ext/imgconverter.py:40
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr ""

#: ext/imgconverter.py:49 ext/imgconverter.py:73
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "परिवर्तक त्रुटि के साथ बहार आ गया:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/imgconverter.py:68
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr ""

#: ext/viewcode.py:258
msgid "highlighting module code... "
msgstr "प्रभाग निर्देश विशिष्ट रूप से दर्शाया जा रहा है..."

#: ext/viewcode.py:286
msgid "[docs]"
msgstr "[docs]"

#: ext/viewcode.py:306
msgid "Module code"
msgstr "प्रभाग निर्देश"

#: ext/viewcode.py:312
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>%s का स्रोत निर्देश </h1>"

#: ext/viewcode.py:338
msgid "Overview: module code"
msgstr "सिंहावलोकन: प्रभाग निर्देश"

#: ext/viewcode.py:339
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>सभी प्रभाग जिनके लिए निर्देश उपलब्ध है</h1>"

#: ext/coverage.py:47
#, python-format
msgid "invalid regex %r in %s"
msgstr "अमान्य रेगएक्स #regex# %r, %s में "

#: ext/coverage.py:134 ext/coverage.py:280
#, python-format
msgid "module %s could not be imported: %s"
msgstr "प्रभाग %s का आयत नहीं किया जा सका: %s"

#: ext/coverage.py:141
#, python-format
msgid ""
"the following modules are documented but were not specified in "
"coverage_modules: %s"
msgstr ""

#: ext/coverage.py:149
msgid ""
"the following modules are specified in coverage_modules but were not "
"documented"
msgstr ""

#: ext/coverage.py:163
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr "स्रोतों की व्यापकता की जांच पूरी, परिणाम %(outdir)spython.txt में देखें. "

#: ext/coverage.py:177
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "अमान्य रेगएक्स #regex# %r, coverage_c_regexes में "

#: ext/coverage.py:245
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr ""

#: ext/coverage.py:429
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr ""

#: ext/coverage.py:445
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr ""

#: ext/coverage.py:458
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr ""

#: ext/todo.py:71
msgid "Todo"
msgstr "अपूर्ण "

#: ext/todo.py:104
#, python-format
msgid "TODO entry found: %s"
msgstr "अपूर्ण प्रविष्टि मिली: %s "

#: ext/todo.py:163
msgid "<<original entry>>"
msgstr "<<मूल प्रविष्टि>>"

#: ext/todo.py:165
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(<<मूल प्रविष्टि>> %s, पंक्ति %d में उपस्थित है.)"

#: ext/todo.py:175
msgid "original entry"
msgstr "मौलिक प्रविष्टि"

#: ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr ""

#: ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "'%s' विकल्प में अनुपस्थित '+' या '-'."

#: ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' एक मान्य विकल्प नहीं है."

#: ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' एक मान्य पाईवर्शन #pyversion# विकल्प नहीं है. "

#: ext/doctest.py:220
msgid "invalid TestCode type"
msgstr "अमान्य टेस्टकोड का प्रकार "

#: ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "स्रोतों में डॉकटेस्ट्स की जांच पूरी, परिणाम %(outdir)s/output.txt में देखें. "

#: ext/doctest.py:434
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "%s भाग में %s पर कोई निर्देश / परिणाम नहीं: %s"

#: ext/doctest.py:522
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "अमान्य डॉकटेस्ट निर्देश की उपेक्षा की जा रही है: %r"

#: ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "ग्राफविज़ निर्देश में दोनों मापदंड, विषय-वस्तु और फाइल का नाम, नहीं हो सकते"

#: ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "बाहरी ग्राफविज़ फाइल %r नहीं मिली अथवा पढने में असफलता मिली"

#: ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "विषय-वस्तु के बिना ग्राफविज़ निर्देश की उपेक्षा की जा रही है. "

#: ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "डॉट निर्देश %r नहीं चलाया जा सकता (ग्राफविज़ परिणाम के लिए आवश्यक), ग्राफविज़_डॉट मान की जांच करें"

#: ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "डॉट त्रुटि के साथ बहार आ गया:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "डॉट ने किसी परिणाम फाइल का नहीं बनाया:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "ग्राफविज़_आउटपुट_फॉर्मेट का 'पी.एन.जी', 'एस.वी.जी.', होना आवश्यक है, पर यह %r है"

#: ext/graphviz.py:333 ext/graphviz.py:386 ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr "डॉट निर्देश %r: %s"

#: ext/graphviz.py:436 ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[graph: %s]"

#: ext/graphviz.py:438 ext/graphviz.py:446
msgid "[graph]"
msgstr "[graph]"

#: ext/imgmath.py:369 ext/mathjax.py:52
msgid "Link to this equation"
msgstr ""

#: ext/apidoc.py:85
#, python-format
msgid "Would create file %s."
msgstr "%s फाइल बन जाएगी."

#: ext/apidoc.py:375
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\n<MODULE_PATH> में पाइथन प्रभाग और पैकेज की पुनरावर्तित खोज करें और\nस्वतःप्रभाग निर्देश द्वारा <OUTPUT_PATH> में प्रति पैकेज एक रेस्ट #reST# फाइल बनाएं.\n\n<EXCLUDE_PATTERN> फाइल और/ अथवा निर्देशिका स्वरुप हो सकते हैं\nजो निर्माण प्रकिया में छोड़ दिए जाएंगे.\n\nनोट: सामान्यतया यह स्क्रिप्ट किसी पहले से बनाई गई फाइल पर पुनर्लेखन नहीं करती."

#: ext/apidoc.py:392
msgid "path to module to document"
msgstr "प्रभाग से लेखपत्र का पथ"

#: ext/apidoc.py:396
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "fnmatch-style फाइल और/ अथवा निर्देशिका स्वरुप जो निर्माण प्रक्रिया से छोड़ने हैं"

#: ext/apidoc.py:407
msgid "directory to place all output"
msgstr "सभी परिणामों को सहेजने के लिए निर्देशिका"

#: ext/apidoc.py:422
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "विषय-सूची में दिखाए जाने वाले उपप्रभागों की अधिकतम गहराई (मानक: 4)"

#: ext/apidoc.py:429
msgid "overwrite existing files"
msgstr "मौजूदा फाइलों पर पुनर्लेखन करें"

#: ext/apidoc.py:437
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "सांकेतिक कड़ियों का अनुसरण करें. कलेक्टिव.रेसिपी.ऑमलेट के साथ प्रभावशाली. "

#: ext/apidoc.py:446
msgid "run the script without creating files"
msgstr "फाइलों को बनाए बिना स्क्रिप्ट चलाएं "

#: ext/apidoc.py:453
msgid "put documentation for each module on its own page"
msgstr "प्रत्येक प्रभाग के आलेख उसके अपने पृष्ठ में रखें"

#: ext/apidoc.py:460
msgid "include \"_private\" modules"
msgstr "\"_private\" प्रभाग को सम्मिलित करें "

#: ext/apidoc.py:467
msgid "filename of table of contents (default: modules)"
msgstr "विषय-सूची की फाइल का नाम (मानक: प्रभाग) "

#: ext/apidoc.py:474
msgid "don't create a table of contents file"
msgstr "विषय-सूची की फाइल न बनाएं "

#: ext/apidoc.py:481
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "प्रभाग/पैकेज पैकेजों का शीर्षक न बनाएं (उदाहरणार्थ, जब डॉकस्ट्रिंग्स में यह पहले से हों) "

#: ext/apidoc.py:492
msgid "put module documentation before submodule documentation"
msgstr " मुख्य प्रभाग के आलेख को उपप्रभाग के आलेख से पहले रखें"

#: ext/apidoc.py:498
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "प्रभाग पथ की व्याख्या 'पी.ई.पी.-0420 निहित नामराशि विवरण' के आधार पर करें "

#: ext/apidoc.py:508
msgid "file suffix (default: rst)"
msgstr "फाइल प्रत्यय (मानक: rst)"

#: ext/apidoc.py:515 ext/autosummary/generate.py:839
msgid "Remove existing files in the output directory that were not generated"
msgstr ""

#: ext/apidoc.py:524
msgid "generate a full project with sphinx-quickstart"
msgstr "स्फिंक्स-त्वरित-आरम्भ के साथ पूर्ण परियोजना उत्पन्न करें "

#: ext/apidoc.py:531
msgid "append module_path to sys.path, used when --full is given"
msgstr "मोड्यूल_पाथ #module_path# को सिस.पाथ #sys.path# में जोड़ें, जब --full दिया जाता है तब इसका प्रयोग होता है "

#: ext/apidoc.py:538
msgid "project name (default: root module name)"
msgstr "परियोजना का नाम (मानक: मूल प्रभाग का नाम) "

#: ext/apidoc.py:545
msgid "project author(s), used when --full is given"
msgstr "परियोजना लेखक(गण), जब --full दिया जाता है तब इसका प्रयोग होता है "

#: ext/apidoc.py:552
msgid "project version, used when --full is given"
msgstr "परियोजना संस्करण, जब --full दिया जाता है तब इसका प्रयोग होता है "

#: ext/apidoc.py:559
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "परियोजना आवृत्ति, जब --full दिया जाता है तब इसका प्रयोग होता है "

#: ext/apidoc.py:564
msgid "extension options"
msgstr "आयाम विकल्प "

#: ext/apidoc.py:638
#, python-format
msgid "%s is not a directory."
msgstr "%s एक निर्देशिका नहीं है. "

#: ext/apidoc.py:710 ext/autosummary/generate.py:875
#, python-format
msgid "Failed to remove %s: %s"
msgstr ""

#: ext/autosectionlabel.py:48
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr ""

#: domains/std/__init__.py:703 domains/std/__init__.py:812
#: ext/autosectionlabel.py:52
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "प्रतिरूप शीर्षक %s, दूसरी प्रतिकृति %s में है "

#: ext/duration.py:85
msgid ""
"====================== slowest reading durations ======================="
msgstr ""

#: ext/imgmath.py:159
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "लाटेक्स आदेश %r नहीं चलाया जा सकता (गणित दिखाने के लिए आवश्यक). आई.एम्.जी.मैथ_लाटेक्स मान की जाँच करें"

#: ext/imgmath.py:174
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "%s आदेश %r नहीं चलाया जा सकता (गणित दिखाने के लिए आवश्यक). imgmath_%s मान की जाँच करें"

#: ext/imgmath.py:328
#, python-format
msgid "display latex %r: %s"
msgstr "लाटेक्स दिखाएँ %r: %s"

#: ext/imgmath.py:362
#, python-format
msgid "inline latex %r: %s"
msgstr "पंक्तिबद्ध लाटेक्स %r: %s"

#: writers/latex.py:1090 writers/manpage.py:263 writers/texinfo.py:662
msgid "Footnotes"
msgstr "पाद टिप्पणियां"

#: writers/manpage.py:309 writers/text.py:936
#, python-format
msgid "[image: %s]"
msgstr "[चित्र: %s]"

#: writers/manpage.py:310 writers/text.py:937
msgid "[image]"
msgstr "[चित्र]"

#: writers/html5.py:99 writers/html5.py:108
msgid "Link to this definition"
msgstr ""

#: writers/html5.py:415
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "%s के लिए नमफिग_फॉर्मेट नहीं बताया गया है"

#: writers/html5.py:427
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "%s बिंदु के लिए कोई पहचान-चिन्ह नहीं दिया गया"

#: writers/html5.py:482
msgid "Link to this term"
msgstr ""

#: writers/html5.py:525 writers/html5.py:530
msgid "Link to this heading"
msgstr ""

#: writers/html5.py:535
msgid "Link to this table"
msgstr ""

#: writers/html5.py:549 writers/latex.py:1099
#, python-format
msgid "unsupported rubric heading level: %s"
msgstr ""

#: writers/html5.py:613
msgid "Link to this code"
msgstr ""

#: writers/html5.py:615
msgid "Link to this image"
msgstr ""

#: writers/html5.py:617
msgid "Link to this toctree"
msgstr ""

#: writers/html5.py:758
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "चित्र का आकार नहीं मिल सका. :scale: विकल्प की उपेक्षा की जा रही है."

#: builders/latex/__init__.py:205 domains/std/__init__.py:646
#: domains/std/__init__.py:658 templates/latex/latex.tex.jinja:106
#: themes/basic/genindex-single.html:22 themes/basic/genindex-single.html:48
#: themes/basic/genindex-split.html:3 themes/basic/genindex-split.html:6
#: themes/basic/genindex.html:3 themes/basic/genindex.html:26
#: themes/basic/genindex.html:59 themes/basic/layout.html:127
#: writers/texinfo.py:513
msgid "Index"
msgstr "अनुक्रमणिका"

#: writers/latex.py:743 writers/texinfo.py:644
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "पाया गया शीर्ष बिंदु किसी भाग, प्रसंग, तालिका, विषय-प्रबोध अथवा पार्श्व-स्थान में नहीं है"

#: writers/texinfo.py:1216
msgid "caption not inside a figure."
msgstr "शीर्षक रेखाचित्र के भीतर नहीं है"

#: writers/texinfo.py:1302
#, python-format
msgid "unimplemented node type: %r"
msgstr "अकार्यान्वित बिंदु प्रकार: %r"

#: writers/latex.py:360
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "अज्ञात %r उच्चतमस्तर_विभाजन #toplevel_sectioning# %r वर्ग के लिए"

#: builders/latex/__init__.py:223 writers/latex.py:410
#, python-format
msgid "no Babel option known for language %r"
msgstr "%r भाषा के लिए कोई बाबेल विकल्प नहीं "

#: writers/latex.py:428
msgid "too large :maxdepth:, ignored."
msgstr "अत्याधिक अधिकतम गहराई # :maxdepth: #, उपेक्षित किया गया."

#: writers/latex.py:590
#, python-format
msgid "template %s not found; loading from legacy %s instead"
msgstr ""

#: writers/latex.py:708
msgid "document title is not a single Text node"
msgstr "लेखपत्र का शीर्षक एकल पाठ बिंदु नहीं है"

#: writers/latex.py:1175
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "दोनों तालिका-स्तंभ और :चौड़ाई: विकल्प दिए गए हैं. :चौड़ाई:  मान की उपेक्षा की जाएगी."

#: writers/latex.py:1573
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "परिमाण मात्रक %s अमान्य है. उपेक्षा की जाएगी."

#: writers/latex.py:1931
#, python-format
msgid "unknown index entry type %s found"
msgstr "अनुक्रमणिका की प्रविष्टि का प्रकार %s मिला"

#: domains/std/__init__.py:87 domains/std/__init__.py:104
#, python-format
msgid "environment variable; %s"
msgstr "परिस्थिति चर पद; %s"

#: domains/std/__init__.py:112
#, python-format
msgid "%s; configuration value"
msgstr ""

#: domains/std/__init__.py:166
msgid "Type"
msgstr ""

#: domains/std/__init__.py:176
msgid "Default"
msgstr ""

#: domains/std/__init__.py:235
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "अशुद्ध रूप विकल्प विवरण %r, अपेक्षित प्रारूप \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" अथवा \"+opt args\""

#: domains/std/__init__.py:306
#, python-format
msgid "%s command line option"
msgstr ""

#: domains/std/__init__.py:308
msgid "command line option"
msgstr ""

#: domains/std/__init__.py:430
msgid "glossary term must be preceded by empty line"
msgstr ""

#: domains/std/__init__.py:438
msgid "glossary terms must not be separated by empty lines"
msgstr ""

#: domains/std/__init__.py:444 domains/std/__init__.py:457
msgid "glossary seems to be misformatted, check indentation"
msgstr ""

#: domains/std/__init__.py:602
msgid "glossary term"
msgstr "पारिभाषिक पद"

#: domains/std/__init__.py:603
msgid "grammar token"
msgstr "व्याकरण संकेत "

#: domains/std/__init__.py:604
msgid "reference label"
msgstr "सन्दर्भ शीर्षक"

#: domains/std/__init__.py:607
msgid "environment variable"
msgstr "परिस्थिति चर पद "

#: domains/std/__init__.py:608
msgid "program option"
msgstr "प्रोग्राम विकल्प "

#: domains/std/__init__.py:609
msgid "document"
msgstr "लेखपत्र"

#: domains/std/__init__.py:647 domains/std/__init__.py:659
msgid "Module Index"
msgstr "प्रभाग सूची"

#: domains/std/__init__.py:648 domains/std/__init__.py:660
#: themes/basic/defindex.html:18
msgid "Search Page"
msgstr "खोज पृष्ठ"

#: domains/std/__init__.py:722
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr ""

#: domains/std/__init__.py:932
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig असमर्थ है. :numref: उपेक्षित है."

#: domains/std/__init__.py:940
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr ""

#: domains/std/__init__.py:952
#, python-format
msgid "the link has no caption: %s"
msgstr "कड़ी का कोई शीर्षक नहीं है: %s"

#: domains/std/__init__.py:966
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "अमान्य numfig_format: %s (%r)"

#: domains/std/__init__.py:969
#, python-format
msgid "invalid numfig_format: %s"
msgstr "अमान्य numfig_format: %s"

#: domains/std/__init__.py:1200
#, python-format
msgid "undefined label: %r"
msgstr ""

#: domains/std/__init__.py:1202
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr ""

#: domains/python/__init__.py:107 domains/python/__init__.py:244
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (%s प्रभाग में )"

#: domains/python/__init__.py:167 domains/python/__init__.py:334
#: domains/python/__init__.py:385 domains/python/__init__.py:424
#, python-format
msgid "%s (in module %s)"
msgstr "%s (%s प्रभाग में )"

#: domains/python/__init__.py:169
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (अंतर्निर्मित चर पद)"

#: domains/python/__init__.py:194
#, python-format
msgid "%s (built-in class)"
msgstr "%s (अंतर्निर्मित वर्ग)"

#: domains/python/__init__.py:195
#, python-format
msgid "%s (class in %s)"
msgstr "%s (%s वर्ग में)"

#: domains/python/__init__.py:249
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (%s वर्ग विधि) "

#: domains/python/__init__.py:251
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (%s स्थैतिक विधि)"

#: domains/python/__init__.py:389
#, python-format
msgid "%s (%s property)"
msgstr ""

#: domains/python/__init__.py:428
#, python-format
msgid "%s (type alias in %s)"
msgstr ""

#: domains/python/__init__.py:559
msgid "Python Module Index"
msgstr "पाइथन प्रभाग सूची"

#: domains/python/__init__.py:560
msgid "modules"
msgstr "प्रभाग"

#: domains/python/__init__.py:637
msgid "Deprecated"
msgstr "अवमानित "

#: domains/python/__init__.py:663
msgid "exception"
msgstr "अपवाद "

#: domains/python/__init__.py:665
msgid "class method"
msgstr "वर्ग विधि"

#: domains/python/__init__.py:666
msgid "static method"
msgstr "स्थैतिक पद्धति"

#: domains/python/__init__.py:668
msgid "property"
msgstr ""

#: domains/python/__init__.py:669
msgid "type alias"
msgstr ""

#: domains/python/__init__.py:729
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: domains/python/__init__.py:858
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "पारस्परिक-सन्दर्भों के लिए एक से अधिक लक्ष्य मिले %r: %s"

#: domains/python/__init__.py:920
msgid " (deprecated)"
msgstr "(अवमानित)"

#: domains/c/__init__.py:304 domains/cpp/__init__.py:441
#: domains/python/_object.py:164 ext/napoleon/docstring.py:786
msgid "Parameters"
msgstr "मापदण्ड"

#: domains/python/_object.py:169
msgid "Variables"
msgstr "चर पद "

#: domains/python/_object.py:173
msgid "Raises"
msgstr "उभारता है "

#: domains/c/__init__.py:199
#, python-format
msgid "%s (C %s)"
msgstr ""

#: domains/c/__init__.py:260 domains/c/_symbol.py:510
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr ""

#: domains/c/__init__.py:307 domains/cpp/__init__.py:454
msgid "Return values"
msgstr ""

#: domains/c/__init__.py:679 domains/cpp/__init__.py:860
msgid "member"
msgstr "सदस्य"

#: domains/c/__init__.py:680
msgid "variable"
msgstr "चर पद"

#: domains/c/__init__.py:682
msgid "macro"
msgstr "मैक्रो"

#: domains/c/__init__.py:683
msgid "struct"
msgstr ""

#: domains/c/__init__.py:684 domains/cpp/__init__.py:858
msgid "union"
msgstr "युग्म"

#: domains/c/__init__.py:685 domains/cpp/__init__.py:863
msgid "enum"
msgstr "गणक"

#: domains/c/__init__.py:686 domains/cpp/__init__.py:864
msgid "enumerator"
msgstr "प्रगणक "

#: domains/c/__init__.py:687 domains/cpp/__init__.py:861
msgid "type"
msgstr "प्रकार"

#: domains/c/__init__.py:689 domains/cpp/__init__.py:866
msgid "function parameter"
msgstr ""

#: domains/cpp/__init__.py:155
msgid "Template Parameters"
msgstr "नमूना मानदण्ड "

#: domains/cpp/__init__.py:277
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: domains/cpp/__init__.py:360 domains/cpp/_symbol.py:793
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr ""

#: domains/cpp/__init__.py:862
msgid "concept"
msgstr "अवधारणा "

#: domains/cpp/__init__.py:867
msgid "template parameter"
msgstr ""

#: themes/haiku/layout.html:16
msgid "Contents"
msgstr "विषय सामिग्री"

#: themes/agogo/layout.html:29 themes/basic/globaltoc.html:2
#: themes/basic/localtoc.html:4 themes/scrolls/layout.html:32
msgid "Table of Contents"
msgstr "विषय-सूची"

#: themes/agogo/layout.html:34 themes/basic/layout.html:130
#: themes/basic/search.html:3 themes/basic/search.html:15
msgid "Search"
msgstr "खोज"

#: themes/agogo/layout.html:37 themes/basic/searchbox.html:8
#: themes/basic/searchfield.html:12
msgid "Go"
msgstr "चलिए"

#: themes/agogo/layout.html:81 themes/basic/sourcelink.html:7
msgid "Show Source"
msgstr "स्रोत दिखाएँ"

#: themes/classic/layout.html:12 themes/classic/static/sidebar.js.jinja:51
msgid "Collapse sidebar"
msgstr "किनारे का स्थान घटाएं"

#: themes/basic/layout.html:18
msgid "Navigation"
msgstr "संचालन"

#: themes/basic/layout.html:115
#, python-format
msgid "Search within %(docstitle)s"
msgstr "%(docstitle)s में खोजें"

#: themes/basic/layout.html:124
msgid "About these documents"
msgstr "इन लेखपत्रों के बारे में"

#: themes/basic/layout.html:133 themes/basic/layout.html:177
#: themes/basic/layout.html:179
msgid "Copyright"
msgstr "सर्वाधिकार"

#: themes/basic/layout.html:183 themes/basic/layout.html:189
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: themes/basic/layout.html:201
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "अंतिम बार सम्पादित %(last_updated)s."

#: themes/basic/layout.html:204
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr ""

#: themes/basic/genindex-single.html:26
#, python-format
msgid "Index &#x2013; %(key)s"
msgstr ""

#: themes/basic/genindex-single.html:54 themes/basic/genindex-split.html:16
#: themes/basic/genindex-split.html:30 themes/basic/genindex.html:65
msgid "Full index on one page"
msgstr "एक पृष्ठ पर पूरी अनुक्रमणिका"

#: themes/basic/genindex-split.html:8
msgid "Index pages by letter"
msgstr "अक्षर द्वारा अनुक्रमित पृष्ठ"

#: themes/basic/genindex-split.html:17
msgid "can be huge"
msgstr "बृहदाकार हो सकता है"

#: themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr " %(docstitle)s में खोजें"

#: themes/basic/sourcelink.html:4
msgid "This Page"
msgstr "यह पृष्ठ "

#: themes/basic/defindex.html:4
msgid "Overview"
msgstr "सिंहावलोकन"

#: themes/basic/defindex.html:8
msgid "Welcome! This is"
msgstr "नमस्ते! यह है"

#: themes/basic/defindex.html:9
msgid "the documentation for"
msgstr "आलेख विषय"

#: themes/basic/defindex.html:10
msgid "last updated"
msgstr "अंतिम परिवर्धन"

#: themes/basic/defindex.html:13
msgid "Indices and tables:"
msgstr "सूचियाँ और सारणियाँ:"

#: themes/basic/defindex.html:16
msgid "Complete Table of Contents"
msgstr "विस्तृत विषय-सूची"

#: themes/basic/defindex.html:17
msgid "lists all sections and subsections"
msgstr "सभी अनुभागों एवं उप-अनुभागों की सूची"

#: themes/basic/defindex.html:19
msgid "search this documentation"
msgstr "इस आलेख में खोजें"

#: themes/basic/defindex.html:21
msgid "Global Module Index"
msgstr "सार्वभौमिक प्रभाग सूची"

#: themes/basic/defindex.html:22
msgid "quick access to all modules"
msgstr "सभी प्रभाग तक तुरंत पहुँच"

#: builders/html/__init__.py:507 themes/basic/defindex.html:23
msgid "General Index"
msgstr "सामान्य अनुक्रमाणिका"

#: themes/basic/defindex.html:24
msgid "all functions, classes, terms"
msgstr "सभी कार्ययुक्तियां, वर्ग, शब्द"

#: themes/basic/searchbox.html:4
msgid "Quick search"
msgstr "त्वरित खोज"

#: themes/basic/search.html:20
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "खोज कार्य के लिए जावा स्क्रिप्ट का होना आवश्यक है. कृपया जावा स्क्रिप्ट को शुरू करें."

#: themes/basic/search.html:28
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr ""

#: themes/basic/search.html:35
msgid "search"
msgstr "खोज"

#: themes/basic/relations.html:4
msgid "Previous topic"
msgstr "पिछला प्रकरण"

#: themes/basic/relations.html:6
msgid "previous chapter"
msgstr "पिछला अध्याय"

#: themes/basic/relations.html:11
msgid "Next topic"
msgstr "अगला प्रकरण"

#: themes/basic/relations.html:13
msgid "next chapter"
msgstr "अगला अध्याय"

#: themes/classic/static/sidebar.js.jinja:42
msgid "Expand sidebar"
msgstr "किनारे का स्थान बढ़ाएं"

#: themes/basic/changes/frameset.html:5
#: themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "परिवर्तित संस्करण %(version)s &#8212; %(docstitle)s"

#: themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "संस्करण %(version)s में स्वतः रचित परिवर्तनों की सूची"

#: themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "पुस्तकालय में परिवर्तन"

#: themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "सी ऐ.पी.आई. परिवर्तन"

#: themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "अन्य परिवर्तन"

#: themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "खोजे गए जोड़े छिपाएं"

#: themes/basic/static/searchtools.js:117
msgid "Search Results"
msgstr "खोज परीणाम "

#: themes/basic/static/searchtools.js:119
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "आपके खोज परिणामों में कोई प्रलेख नहीं मिला. कृपया सुनिश्चित करें कि सभी शब्दों की वर्तनी शुद्ध है और आपने यथेष्ट श्रेणियां चुनी हैं."

#: themes/basic/static/searchtools.js:123
msgid "Search finished, found one page matching the search query."
msgid_plural ""
"Search finished, found ${resultCount} pages matching the search query."
msgstr[0] ""
msgstr[1] ""

#: themes/basic/static/searchtools.js:253
msgid "Searching"
msgstr "खोज जारी"

#: themes/basic/static/searchtools.js:270
msgid "Preparing search..."
msgstr "खोज की तैयारी"

#: themes/basic/static/searchtools.js:474
msgid ", in "
msgstr ", में "

#: environment/collectors/asset.py:96
#, python-format
msgid "image file not readable: %s"
msgstr "चित्र फाइल पठनीय नहीं है: %s"

#: environment/collectors/asset.py:124
#, python-format
msgid "image file %s not readable: %s"
msgstr "चित्र फाइल %s पठनीय नहीं है: %s"

#: environment/collectors/asset.py:161
#, python-format
msgid "download file not readable: %s"
msgstr "उतारी गई फाइल पठनीय नहीं है: %s"

#: environment/collectors/toctree.py:258
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s में पहले से भाग संख्या नियत है (एक के अन्दर दूसरा अंकित विषय-सूची-संरचना)"

#: environment/adapters/toctree.py:318
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "पारस्परिक संदर्भित विषय-सूची-संरचना सन्दर्भ पाए गए, उपेक्षा की जा रही है: %s <- %s"

#: environment/adapters/toctree.py:342
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "विषय-सूची-संरचना में लेखपत्र %r, जिसका कोई शीर्षक नहीं है, का सन्दर्भ है: कोई सम्बन्ध नहीं बनाया जा सकेगा"

#: environment/adapters/toctree.py:357
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr ""

#: environment/adapters/indexentries.py:126
#, python-format
msgid "see %s"
msgstr "%s देखिए"

#: environment/adapters/indexentries.py:136
#, python-format
msgid "see also %s"
msgstr "%s भी देखिए"

#: environment/adapters/indexentries.py:144
#, python-format
msgid "unknown index entry type %r"
msgstr "अनुक्रमणिका की प्रविष्टि का प्रकार अज्ञात %r"

#: environment/adapters/indexentries.py:270
#: templates/latex/sphinxmessages.sty.jinja:11
msgid "Symbols"
msgstr "संकेत "

#: builders/html/_build_info.py:32
msgid "failed to read broken build info file (unknown version)"
msgstr ""

#: builders/html/_build_info.py:36
msgid "failed to read broken build info file (missing config entry)"
msgstr ""

#: builders/html/_build_info.py:39
msgid "failed to read broken build info file (missing tags entry)"
msgstr ""

#: builders/html/__init__.py:113
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "एच.टी.एम्.एल. पृष्ठ %(outdir)sमें हैं."

#: builders/html/__init__.py:348
#, python-format
msgid "Failed to read build info file: %r"
msgstr "निर्माण सूचनापत्र फाइल को नहीं पढ़ा जा सका: %r"

#: builders/html/__init__.py:363
msgid "build_info mismatch, copying .buildinfo to .buildinfo.bak"
msgstr ""

#: builders/html/__init__.py:366
msgid "building [html]: "
msgstr ""

#: builders/html/__init__.py:382
#, python-format
msgid ""
"template %s has been changed since the previous build, all docs will be "
"rebuilt"
msgstr ""

#: builders/html/__init__.py:507
msgid "index"
msgstr "अनुक्रमणिका"

#: builders/html/__init__.py:560
#, python-format
msgid "Logo of %s"
msgstr ""

#: builders/html/__init__.py:589
msgid "next"
msgstr "आगामी"

#: builders/html/__init__.py:598
msgid "previous"
msgstr "पूर्ववर्ती"

#: builders/html/__init__.py:695
msgid "generating indices"
msgstr "अनुक्रमाणिका निर्मित की जा रही है"

#: builders/html/__init__.py:710
msgid "writing additional pages"
msgstr "अतिरिक्त पृष्ठ लिखे जा रहे हैं"

#: builders/html/__init__.py:793
#, python-format
msgid "cannot copy image file '%s': %s"
msgstr ""

#: builders/html/__init__.py:805
msgid "copying downloadable files... "
msgstr "उतारी गई फाइलों की प्रतिलिपि बनाई जा रही है..."

#: builders/html/__init__.py:817
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "उतारी गई फाइलों %r की प्रतिलिपि नहीं की जा सकी: %s"

#: builders/html/__init__.py:863
#, python-format
msgid "Failed to copy a file in the theme's 'static' directory: %s: %r"
msgstr ""

#: builders/html/__init__.py:881
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr ""

#: builders/html/__init__.py:916
msgid "copying static files"
msgstr ""

#: builders/html/__init__.py:933
#, python-format
msgid "cannot copy static file %r"
msgstr "स्थैतिक फाइल %r की प्रतिलिपि नहीं की जा सकी"

#: builders/html/__init__.py:938
msgid "copying extra files"
msgstr "अतिरिक्त फाइलों की प्रतिलिपियां बनाये जा रहे है| "

#: builders/html/__init__.py:948
#, python-format
msgid "cannot copy extra file %r"
msgstr "अतिरिक्त फाइल %r की प्रतिलिपि नहीं की जा सकी"

#: builders/html/__init__.py:954
#, python-format
msgid "Failed to write build info file: %r"
msgstr "निर्माण फाइल को नहीं लिखा जा सका: %r"

#: builders/html/__init__.py:1003
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "खोज अनुक्रमाणिका नहीं चढाई जा सकी, लेकिन सभी लेखपत्र नहीं बनाए जाएंगे: अनुक्रमणिका अपूर्ण रहेगी."

#: builders/html/__init__.py:1051
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "पृष्ठ %s html_sidebars में दो आकृतियों से मिलता है: %r  %r"

#: builders/html/__init__.py:1213
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "पृष्ठ %s की प्रस्तुति करते समय यूनिकोड त्रुटि हुई. कृपया यह सुनिश्चित कर लें कि सभी नॉन-असकी #non-ASCII# विहित विन्यास मान यूनिकोड अक्षरों में हैं."

#: builders/html/__init__.py:1222
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "पृष्ठ %s की प्रस्तुति करते समय एक त्रुटि हुई.\nकारण: %r"

#: builders/html/__init__.py:1255
msgid "dumping object inventory"
msgstr "विषयवस्तुओं का भंडार बनाया जा रहा है"

#: builders/html/__init__.py:1263
#, python-format
msgid "dumping search index in %s"
msgstr "%s में खोज अनुक्रमाणिका भंडार बनाया जा रहा है"

#: builders/html/__init__.py:1306
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "अमान्य js_file: %r, उपेक्षित"

#: builders/html/__init__.py:1339
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "कई math_renderers पंजीकृत हैं. लेकिन कोई math_renderers नहीं चुना गया है."

#: builders/html/__init__.py:1344
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "अज्ञात math_renderer %r दिया गया."

#: builders/html/__init__.py:1358
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "html_extra_path का प्रविष्टि %r outdir में है|  "

#: builders/html/__init__.py:1363
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "html_extra_path प्रविष्टि %r का अस्तित्व नहीं है"

#: builders/html/__init__.py:1378
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "html_static_path का  प्रविष्टि %r outdir में है|  "

#: builders/html/__init__.py:1383
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "html_static_path प्रविष्टि %r का अस्तित्व नहीं है"

#: builders/html/__init__.py:1394 builders/latex/__init__.py:504
#, python-format
msgid "logo file %r does not exist"
msgstr "प्रतीकचिन्ह फाइल %r का अस्तित्व नहीं है"

#: builders/html/__init__.py:1405
#, python-format
msgid "favicon file %r does not exist"
msgstr "इष्ट चिन्ह फाइल %r का अस्तित्व नहीं है"

#: builders/html/__init__.py:1417
#, python-format
msgid ""
"Values in 'html_sidebars' must be a list of strings. At least one pattern "
"has a string value: %s. Change to `html_sidebars = %r`."
msgstr ""

#: builders/html/__init__.py:1430
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: builders/html/__init__.py:1447
#, python-format
msgid "%s %s documentation"
msgstr "%s %s दिग्दर्शिका"

#: builders/latex/transforms.py:118
msgid "Failed to get a docname!"
msgstr ""

#: builders/latex/transforms.py:119
#, python-format
msgid "Failed to get a docname for source %r!"
msgstr ""

#: builders/latex/transforms.py:485
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: builders/latex/__init__.py:117
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "लाटेक्स लेखपत्र %(outdir)s में हैं."

#: builders/latex/__init__.py:119
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\nइन्हें (pdf)latex से चलाने के लिए उस निर्देशिका में 'मेक' आदेश चलायें\n(ऐसा स्वचालित रूप से करने के लिए यहाँ 'make latexpdf' आदेश का उपयोग करें)"

#: builders/latex/__init__.py:157
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "कोई \"latex_documents\" विन्यास मान नहीं मिला; कोई  नहीं लिखे जाएंगे"

#: builders/latex/__init__.py:169
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "\"latex_documents\" विन्यास मान अज्ञात लेखपत्र %s का सन्दर्भ है"

#: builders/latex/__init__.py:208 templates/latex/latex.tex.jinja:91
msgid "Release"
msgstr "आवृत्ति"

#: builders/latex/__init__.py:429
msgid "copying TeX support files"
msgstr "टेक्स सहायक फाइलों की प्रतिलिपि की जा रही है..."

#: builders/latex/__init__.py:466
msgid "copying additional files"
msgstr "अतिरिक्त फाइलों की प्रतिकृति बनाई जा रही है"

#: builders/latex/__init__.py:540
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr ""

#: builders/latex/__init__.py:548
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr ""

#: builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r में कोई \"रूप\" मान नहीं है"

#: builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r में कोई \"%s \" मान नहीं है"

#: _cli/util/errors.py:124
msgid "Exception occurred, starting debugger:"
msgstr ""

#: _cli/util/errors.py:133
msgid "reStructuredText markup error:"
msgstr ""

#: _cli/util/errors.py:168
msgid "The full traceback has been saved in:"
msgstr ""

#: _cli/util/errors.py:172
msgid ""
"To report this error to the developers, please open an issue at "
"<https://github.com/sphinx-doc/sphinx/issues/>. Thanks!"
msgstr ""

#: transforms/post_transforms/__init__.py:125
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: transforms/post_transforms/__init__.py:185
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "किसी भी पारस्परिक-सन्दर्भ के लिए एक से अधिक लक्ष्य मिले %r: %s संभव"

#: transforms/post_transforms/__init__.py:251
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr ""

#: transforms/post_transforms/__init__.py:257
#, python-format
msgid "%r reference target not found: %s"
msgstr ""

#: transforms/post_transforms/images.py:77
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "दूरस्थ चित्र नहीं लाया जा सका: %s [%s]"

#: transforms/post_transforms/images.py:94
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "दूरस्थ चित्र नहीं लाया जा सका: %s [%d]"

#: transforms/post_transforms/images.py:141
#, python-format
msgid "Unknown image format: %s..."
msgstr "अज्ञात चित्र प्रारूप: %s..."

#: ext/napoleon/docstring.py:707
msgid "Example"
msgstr "उदाहरण"

#: ext/napoleon/docstring.py:708
msgid "Examples"
msgstr "कुछ उदाहरण"

#: ext/napoleon/__init__.py:344 ext/napoleon/docstring.py:752
msgid "Keyword Arguments"
msgstr "मुख्य शब्दों के चर-पद"

#: ext/napoleon/docstring.py:768
msgid "Notes"
msgstr "टिप्पणियाँ"

#: ext/napoleon/docstring.py:777
msgid "Other Parameters"
msgstr "अन्य मापदण्ड"

#: ext/napoleon/docstring.py:813
msgid "Receives"
msgstr ""

#: ext/napoleon/docstring.py:817
msgid "References"
msgstr "सन्दर्भ"

#: ext/napoleon/docstring.py:849
msgid "Warns"
msgstr "चेतावनी देता है"

#: ext/napoleon/docstring.py:853
msgid "Yields"
msgstr "मिलता है"

#: ext/napoleon/docstring.py:1015
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr ""

#: ext/napoleon/docstring.py:1022
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr ""

#: ext/napoleon/docstring.py:1029
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr ""

#: ext/napoleon/docstring.py:1036
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr ""

#: ext/autosummary/__init__.py:256
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr ""

#: ext/autosummary/__init__.py:258
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr ""

#: ext/autosummary/__init__.py:277
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr ""

#: ext/autosummary/__init__.py:330
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: ext/autosummary/__init__.py:344
#, python-format
msgid "failed to parse name %s"
msgstr "पद-विच्छेदन में असफलता: %s"

#: ext/autosummary/__init__.py:349
#, python-format
msgid "failed to import object %s"
msgstr "विषय-वस्तु के आयात में असफलता: %s"

#: ext/autosummary/__init__.py:648
#, python-format
msgid ""
"Summarised items should not include the current module. Replace %r with %r."
msgstr ""

#: ext/autosummary/__init__.py:819
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr ""

#: ext/autosummary/__init__.py:827
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: ext/autosummary/generate.py:215 ext/autosummary/generate.py:391
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: ext/autosummary/generate.py:526
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[ऑटोसमरी] अब इसका स्वतःसारांश बना रहा है: %s"

#: ext/autosummary/generate.py:530
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[ऑटोसमरी] %s पर लिख रहा है"

#: ext/autosummary/generate.py:572
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: ext/autosummary/generate.py:767
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\nस्वतः सारांश #autosummary# निर्देश का प्रयोग करते हुए पुर्नसरंचितपाठ बनाता है.\n\nस्फिंक्स-ऑटोजेन स्फिंक्स.एक्स्ट.ऑटोसमरी.जेनेरेट का मुखड़ा है.\nयह प्रदत्त फाइलों में सम्मिलित ऑटो समरी निर्देशों के अनुसार पुर्नसरंचितपाठ बनाता है\n\nस्वतः सारांश #autosummary# निर्देश का प्रारूप स्फिंक्स.एक्स्ट.ऑटोसमरी \nपाइथन प्रभाग में निबंधित है और इसे आप निम्नलिखित माध्यम से पढ़ सकते हैं:\n\n  pydoc sphinx.ext.autosummary\n"

#: ext/autosummary/generate.py:789
msgid "source files to generate rST files for"
msgstr "आर.एस.टी. फाइलें बनाने के लिए स्रोत फाइलें"

#: ext/autosummary/generate.py:797
msgid "directory to place all output in"
msgstr "सभी परिणाम रखने के लिए निर्देशिका"

#: ext/autosummary/generate.py:805
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "फाइलों के लिए मानक प्रत्यय (मानक: %(default)s)"

#: ext/autosummary/generate.py:813
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "पारंपरिक प्रारूप निर्देशिका (मानक: %(default)s)"

#: ext/autosummary/generate.py:821
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "लेखपत्र आयातित सदस्य (मानक: %(default)s)"

#: ext/autosummary/generate.py:829
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr ""

#: ext/intersphinx/_resolve.py:47
#, python-format
msgid "(in %s v%s)"
msgstr "(%s v%s में)"

#: ext/intersphinx/_resolve.py:49
#, python-format
msgid "(in %s)"
msgstr "(%s में)"

#: ext/intersphinx/_resolve.py:103
#, python-format
msgid "inventory '%s': duplicate matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:113
#, python-format
msgid "inventory '%s': multiple matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:359
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:367
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:378
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:585
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: ext/intersphinx/_load.py:59
#, python-format
msgid ""
"Invalid intersphinx project identifier `%r` in intersphinx_mapping. Project "
"identifiers must be non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:70
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Expected a two-element tuple "
"or list."
msgstr ""

#: ext/intersphinx/_load.py:81
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Values must be a (target URI,"
" inventory locations) pair."
msgstr ""

#: ext/intersphinx/_load.py:92
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:101
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique (other instance in intersphinx_mapping[%r])."
msgstr ""

#: ext/intersphinx/_load.py:120
#, python-format
msgid ""
"Invalid inventory location value `%r` in intersphinx_mapping[%r][1]. "
"Inventory locations must be non-empty strings or None."
msgstr ""

#: ext/intersphinx/_load.py:131
msgid "Invalid `intersphinx_mapping` configuration (1 error)."
msgstr ""

#: ext/intersphinx/_load.py:134
#, python-format
msgid "Invalid `intersphinx_mapping` configuration (%s errors)."
msgstr ""

#: ext/intersphinx/_load.py:155
msgid "An invalid intersphinx_mapping entry was added after normalisation."
msgstr ""

#: ext/intersphinx/_load.py:240
#, python-format
msgid "loading intersphinx inventory '%s' from %s ..."
msgstr ""

#: ext/intersphinx/_load.py:265
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "कुछ चीजों के साथ कुछ समस्या है, लेकिन काम के दूसरे विकल्प उपलब्ध हैं: "

#: ext/intersphinx/_load.py:275
msgid "failed to reach any of the inventories with the following issues:"
msgstr "कुछ चीजों पहुँचने में असफलता मिली और यह समस्याएँ मिलीं: "

#: ext/intersphinx/_load.py:319
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "इन्टरस्फिंक्स सामान स्थानांतरित हो चुका है: %s -> %s"

#: ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr ""

#: ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr ""

#: ext/autodoc/__init__.py:142
#, python-format
msgid "invalid value for member-order option: %s"
msgstr ""

#: ext/autodoc/__init__.py:150
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr ""

#: ext/autodoc/__init__.py:415
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "स्वतः %s (%r) के लिए अमान्य हस्ताक्षर"

#: ext/autodoc/__init__.py:532
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "%s के पदों का प्रारूप बनाने में व्यवधान: %s"

#: ext/autodoc/__init__.py:807
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: ext/autodoc/__init__.py:902
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "पता नहीं है कि कौन सा प्रभाग स्वतःप्रलेखन %r के लिए आयात करना है (लेखपत्र में \"प्रभाग\" या \"वर्तमान-प्रभाग\" निर्देश रख कर देखें; अथवा स्पष्ट प्रभाग नाम देकर देखें)"

#: ext/autodoc/__init__.py:946
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: ext/autodoc/__init__.py:965
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:1028
msgid "\"::\" in automodule name doesn't make sense"
msgstr "स्वतः प्रभाग नाम में \"::\"  विवेकहीन है"

#: ext/autodoc/__init__.py:1035
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr "स्वतः-प्रभाग %s के लिए हस्ताक्षर पद अथवा प्रत्युत्तरित टिप्पणी प्रदान की गई"

#: ext/autodoc/__init__.py:1048
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__ अंतिम अक्षरमाला होनी चाहिए, न कि %r (%s प्रभाग में) --  __all__ की उपेक्षा की जाएगी"

#: ext/autodoc/__init__.py:1114
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr ""

#: ext/autodoc/__init__.py:1337 ext/autodoc/__init__.py:1414
#: ext/autodoc/__init__.py:2829
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:1633
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:1760
#, python-format
msgid "Bases: %s"
msgstr "आधार: %s"

#: ext/autodoc/__init__.py:1774
#, python-format
msgid "missing attribute %s in object %s"
msgstr "%s गुण %s वस्तु में अनुपस्थित"

#: ext/autodoc/__init__.py:1855 ext/autodoc/__init__.py:1892
#: ext/autodoc/__init__.py:1987
#, python-format
msgid "alias of %s"
msgstr ""

#: ext/autodoc/__init__.py:1875
#, python-format
msgid "alias of TypeVar(%s)"
msgstr ""

#: ext/autodoc/__init__.py:2217 ext/autodoc/__init__.py:2317
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:2448
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr ""

#: ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr ""

#: templates/latex/longtable.tex.jinja:52
#: templates/latex/sphinxmessages.sty.jinja:8
msgid "continued from previous page"
msgstr "पिछले पृष्ठ से जारी"

#: templates/latex/longtable.tex.jinja:63
#: templates/latex/sphinxmessages.sty.jinja:9
msgid "continues on next page"
msgstr "अगले पृष्ठ पर जारी"

#: templates/latex/sphinxmessages.sty.jinja:10
msgid "Non-alphabetical"
msgstr "अकारादि-क्रमहीन "

#: templates/latex/sphinxmessages.sty.jinja:12
msgid "Numbers"
msgstr "संख्याएं "

#: templates/latex/sphinxmessages.sty.jinja:13
msgid "page"
msgstr "पृष्ठ"
