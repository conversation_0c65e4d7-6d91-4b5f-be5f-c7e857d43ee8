%% OPTIONS FOR GEOMETRY
%
% change this info string if making any custom modification
\ProvidesPackage{sphinxoptionsgeometry}[2021/01/27 geometry]

% geometry
\ifx\kanjiskip\@undefined
  \PassOptionsToPackage{%
     hmargin={\unexpanded{\spx@opt@hmargin}},%
     vmargin={\unexpanded{\spx@opt@vmargin}},%
     marginpar=\unexpanded{\spx@opt@marginpar}}
  {geometry}
\else
 % set text width for Japanese documents to be integer multiple of 1zw
 % and text height to be integer multiple of \baselineskip
 % the execution is delayed to \sphinxsetup then geometry.sty
 \normalsize\normalfont
 \newcommand*\sphinxtextwidthja[1]{%
    \if@twocolumn\tw@\fi
    \dimexpr
       \numexpr\dimexpr\paperwidth-\tw@\dimexpr#1\relax\relax/
               \dimexpr\if@twocolumn\tw@\else\@ne\fi zw\relax
    zw\relax}%
 \newcommand*\sphinxmarginparwidthja[1]{%
    \dimexpr\numexpr\dimexpr#1\relax/\dimexpr1zw\relax zw\relax}%
 \newcommand*\sphinxtextlinesja[1]{%
    \numexpr\@ne+\dimexpr\paperheight-\topskip-\tw@\dimexpr#1\relax\relax/
                 \baselineskip\relax}%
 \ifx\@jsc@uplatextrue\@undefined\else
 % the way we found in order for the papersize special written by
 % geometry in the dvi file to be correct in case of jsbook class
   \ifnum\mag=\@m\else % do nothing special if nomag class option or 10pt
     \PassOptionsToPackage{truedimen}{geometry}%
   \fi
 \fi
 \PassOptionsToPackage{%
    hmarginratio={1:1},%
    textwidth=\unexpanded{\sphinxtextwidthja{\spx@opt@hmargin}},%
    vmarginratio={1:1},%
    lines=\unexpanded{\sphinxtextlinesja{\spx@opt@vmargin}},%
    marginpar=\unexpanded{\sphinxmarginparwidthja{\spx@opt@marginpar}},%
    footskip=2\baselineskip,%
  }{geometry}%
 \AtBeginDocument
 {% update a dimension used by the jsclasses
  \ifx\@jsc@uplatextrue\@undefined\else\fullwidth\textwidth\fi
  % for some reason, jreport normalizes all dimensions with \@settopoint
  \@ifclassloaded{jreport}
    {\@settopoint\textwidth\@settopoint\textheight\@settopoint\marginparwidth}
    {}% <-- "false" clause of \@ifclassloaded
  }%
\fi

\endinput
