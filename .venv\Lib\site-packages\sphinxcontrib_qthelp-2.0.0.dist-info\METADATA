Metadata-Version: 2.1
Name: sphinxcontrib-qthelp
Version: 2.0.0
Summary: sphinxcontrib-qthelp is a sphinx extension which outputs QtHelp documents
Author-email: <PERSON> <<EMAIL>>
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Framework :: Sphinx
Classifier: Framework :: Sphinx :: Extension
Classifier: Topic :: Documentation
Classifier: Topic :: Documentation :: Sphinx
Classifier: Topic :: Text Processing
Classifier: Topic :: Utilities
Requires-Dist: ruff==0.5.5 ; extra == "lint"
Requires-Dist: mypy ; extra == "lint"
Requires-Dist: types-docutils ; extra == "lint"
Requires-Dist: Sphinx>=5 ; extra == "standalone"
Requires-Dist: pytest ; extra == "test"
Requires-Dist: defusedxml>=0.7.1 ; extra == "test"
Project-URL: Changelog, https://github.com/sphinx-doc/sphinxcontrib-qthelp/blob/master/CHANGES.rst
Project-URL: Code, https://github.com/sphinx-doc/sphinxcontrib-qthelp/
Project-URL: Download, https://pypi.org/project/sphinxcontrib-qthelp/
Project-URL: Homepage, https://www.sphinx-doc.org/
Project-URL: Issue tracker, https://github.com/sphinx-doc/sphinx/issues/
Provides-Extra: lint
Provides-Extra: standalone
Provides-Extra: test

====================
sphinxcontrib-qthelp
====================

sphinxcontrib-qthelp is a sphinx extension which outputs QtHelp document.

For more details, please visit http://www.sphinx-doc.org/.

Installing
==========

Install from PyPI::

   pip install -U sphinxcontrib-qthelp

Contributing
============

See `CONTRIBUTING.rst`__

.. __: https://github.com/sphinx-doc/sphinx/blob/master/CONTRIBUTING.rst

