# Translations template for Sphinx.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023-2025
# <AUTHOR> <EMAIL>, 2009
# <PERSON><PERSON> <<EMAIL>>, 2009
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-02-18 18:26+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023-2025\n"
"Language-Team: Catalan (http://app.transifex.com/sphinx-doc/sphinx-1/language/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: extension.py:58
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "L'extensió %s és requerida per la configuració de needs_extensions, però aquesta no està carregada."

#: extension.py:79
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "Aquest projecte necessita l'extensió %s almenys a la versió %s i, per tant, no es pot construir amb la versió carregada (%s)."

#: application.py:212
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "No es pot trobar el directori d'origen (%s)"

#: application.py:217
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "El directori de sortida (%s) no és un directori"

#: application.py:222
msgid "Source directory and destination directory cannot be identical"
msgstr "El directori d'origen i el de destinació no poden ser idèntics"

#: application.py:252
#, python-format
msgid "Running Sphinx v%s"
msgstr "S'està executant Sphinx versió %s"

#: application.py:278
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "Aquest projecte almenys necessita Sphinx versió %s i, per tant, no es pot crear amb aquesta versió."

#: application.py:297
msgid "making output directory"
msgstr "es crea el directori de sortida"

#: application.py:302 registry.py:538
#, python-format
msgid "while setting up extension %s:"
msgstr "mentre es configura l'extensió %s:"

#: application.py:309
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "«setup» tal com es defineix actualment a conf.py no és una crida de Python. Modifiqueu la seva definició per a convertir-la en una funció que es pugui cridar. Això és necessari perquè conf.py es comporti com a una extensió de Sphinx."

#: application.py:346
#, python-format
msgid "loading translations [%s]... "
msgstr "s'estan carregant les traduccions [%s]..."

#: application.py:370 util/display.py:89
msgid "done"
msgstr "fet"

#: application.py:372
msgid "not available for built-in messages"
msgstr "no està disponible per a missatges integrats"

#: application.py:386
msgid "loading pickled environment"
msgstr "s'està carregant l'entorn preparat"

#: application.py:394
#, python-format
msgid "failed: %s"
msgstr "ha fallat: %s"

#: application.py:407
msgid "No builder selected, using default: html"
msgstr "No s'ha seleccionat cap constructor, s'usa el predeterminat: html"

#: application.py:439
msgid "build finished with problems."
msgstr "la compilació ha finalitzat amb problemes."

#: application.py:441
msgid "build succeeded."
msgstr "la compilació ha tingut èxit."

#: application.py:446
msgid ""
"build finished with problems, 1 warning (with warnings treated as errors)."
msgstr "la compilació ha finalitzat amb problemes, 1 avís (amb els avisos tractats com a errors)."

#: application.py:450
msgid "build finished with problems, 1 warning."
msgstr "la compilació ha finalitzat amb problemes, 1 avís."

#: application.py:452
msgid "build succeeded, 1 warning."
msgstr "la compilació ha tingut èxit, 1 avís."

#: application.py:458
#, python-format
msgid ""
"build finished with problems, %s warnings (with warnings treated as errors)."
msgstr "la compilació ha acabat amb problemes, %s avisos (amb avisos tractats com a errors)."

#: application.py:462
#, python-format
msgid "build finished with problems, %s warnings."
msgstr "la compilació ha acabat amb problemes, %s avisos."

#: application.py:464
#, python-format
msgid "build succeeded, %s warnings."
msgstr "la compilació ha tingut èxit, %s avisos."

#: application.py:1026
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "la classe del node %r ja està registrada, els seus visitants seran anul·lats"

#: application.py:1119
#, python-format
msgid "directive %r is already registered and will not be overridden"
msgstr "la directiva %r ja està registrada i no serà sobreseguda"

#: application.py:1145 application.py:1173
#, python-format
msgid "role %r is already registered and will not be overridden"
msgstr "el rol %r ja està registrat i no serà sobresegut"

#: application.py:1770
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "l'extensió %s no declara si és segur per a la lectura en paral·lel, suposant que no ho sigui, demaneu a l'autor de l'extensió que ho comprovi i faci que sigui explícit"

#: application.py:1775
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "l'extensió %s no és segura per a la lectura en paral·lel"

#: application.py:1779
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "l'extensió %s no declara si és segur per a l'escriptura en paral·lel, suposant que no ho sigui, demaneu a l'autor de l'extensió que ho comprovi i faci que sigui explícit"

#: application.py:1784
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "l'extensió %s no és segura per a l'escriptura en paral·lel"

#: application.py:1792 application.py:1796
#, python-format
msgid "doing serial %s"
msgstr "s'està executant %s en sèrie"

#: config.py:355
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "el directori de configuració no conté un fitxer conf.py (%s)"

#: config.py:366
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr "S'ha trobat un valor de configuració no vàlid: «language = None». Actualitzeu la vostra configuració a un codi d'idioma vàlid. Es torna «en» (anglès)."

#: config.py:394
#, python-format
msgid "'%s' must be '0' or '1', got '%s'"
msgstr "«%s» ha de ser «0» o «1», s'ha obtingut «%s»"

#: config.py:399
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "no s'ha pogut substituir l'ajust de la configuració del diccionari %r, s'ignora (useu %r per a establir elements individuals)"

#: config.py:411
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "nombre no vàlid %r del valor de configuració %r, s'ignora"

#: config.py:419
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "no s'ha pogut substituir l'ajust de la configuració %r amb tipus no compatibles, s'ignora"

#: config.py:442
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "valor de configuració desconegut %r en substituir, s'ignora"

#: config.py:496
#, python-format
msgid "No such config value: %r"
msgstr "Aquest valor de configuració no existeix: %r"

#: config.py:524
#, python-format
msgid "Config value %r already present"
msgstr "El valor de configuració %r ja està present"

#: config.py:561
#, python-format
msgid ""
"cannot cache unpickleable configuration value: %r (because it contains a "
"function, class, or module object)"
msgstr "no es pot emmagatzemar a la memòria cau un valor de configuració no recuperable: %r (perquè conté un objecte de funció, classe o mòdul)"

#: config.py:603
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "Hi ha un error de sintaxi en el fitxer de configuració: %s\n"

#: config.py:607
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "El fitxer de configuració (o un dels mòduls que s'importen) ha cridat «sys.exit()»"

#: config.py:615
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "Hi ha un error programable en el fitxer de configuració:\n\n%s"

#: config.py:637
#, python-format
msgid "Failed to convert %r to a frozenset"
msgstr ""

#: config.py:655 config.py:663
#, python-format
msgid "Converting `source_suffix = %r` to `source_suffix = %r`."
msgstr "S'està convertint «source_suffix = %r» a «source_suffix = %r»."

#: config.py:669
#, python-format
msgid ""
"The config value `source_suffix' expects a dictionary, a string, or a list "
"of strings. Got `%r' instead (type %s)."
msgstr "El valor de configuració «source_suffix» espera un diccionari, una cadena o una llista de cadenes. En lloc d'això, s'ha obtingut «%r» (escriviu %s)."

#: config.py:690
#, python-format
msgid "Section %s"
msgstr "Secció %s"

#: config.py:691
#, python-format
msgid "Fig. %s"
msgstr "Fig. %s"

#: config.py:692
#, python-format
msgid "Table %s"
msgstr "Taula %s"

#: config.py:693
#, python-format
msgid "Listing %s"
msgstr "Llistat %s"

#: config.py:802
#, python-brace-format
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "El valor de configuració «{name}» ha de ser un de {candidates}, però s'ha donat «{current}»."

#: config.py:833
#, python-brace-format
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "El valor de configuració «{name}» té el tipus «{current.__name__}», s'espera {permitted}."

#: config.py:850
#, python-brace-format
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "El valor de configuració «{name}» té el tipus «{current.__name__}», el valor predeterminat és «{default.__name__}»."

#: config.py:862
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "no s'ha trobat primary_domain %r, s'ignora."

#: config.py:882
msgid ""
"Sphinx now uses \"index\" as the master document by default. To keep pre-2.0"
" behaviour, set \"master_doc = 'contents'\"."
msgstr ""

#: highlighting.py:170
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "No es coneix el nom del lexer de pigments %r"

#: highlighting.py:209
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr "L'anàlisi lèxica del literal_block %r com a «%s» ha resultat en un error en el testimoni: %r. S'està tornant a provar en el mode relaxat."

#: theming.py:115
#, python-format
msgid ""
"Theme configuration sections other than [theme] and [options] are not "
"supported (tried to get a value from %r)."
msgstr "No s'admeten les seccions de configuració del tema que no siguin [theme] i [options] (s'ha intentat obtenir un valor des de %r)."

#: theming.py:120
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "configuració %s. %s no es produeix en cap de les configuracions de temes cercats"

#: theming.py:135
#, python-format
msgid "unsupported theme option %r given"
msgstr "opció de tema no admesa, s'ha donat %r"

#: theming.py:208
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "el fitxer %r en el camí de temes no és un fitxer ZIP vàlid ni conté cap tema"

#: theming.py:228
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr "no s'ha trobat cap tema anomenat %r (manca theme.toml?)"

#: theming.py:268
#, python-format
msgid "The %r theme has circular inheritance"
msgstr "El tema %r té una herència circular"

#: theming.py:276
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr "El tema %r hereta des de %r, el qual no és un tema que estigui carregat. Els temes carregats són: %s"

#: theming.py:282
#, python-format
msgid "The %r theme has too many ancestors"
msgstr "El tema %r té massa avantpassats"

#: theming.py:310
#, python-format
msgid "no theme configuration file found in %r"
msgstr "no s'ha trobat cap fitxer de configuració del tema a %r"

#: theming.py:335 theming.py:388
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr "el tema %r no té la taula «theme»"

#: theming.py:339
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr "La taula del tema %r «[theme]» no és una taula"

#: theming.py:343 theming.py:391
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr "El tema %r ha de definir la configuració «theme.inherit»"

#: theming.py:347
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr "La taula del tema %r «[options]» no és una taula"

#: theming.py:366
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr "La configuració de «theme.pygments_style» ha de ser una taula. Consell: «%s»"

#: events.py:77
#, python-format
msgid "Event %r already present"
msgstr "L'esdeveniment %r ja està present"

#: events.py:370
#, python-format
msgid "Unknown event name: %s"
msgstr "Nom desconegut de l'esdeveniment: %s"

#: events.py:416
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr "El gestor %r per a l'esdeveniment %r ha retornat una excepció"

#: project.py:72
#, python-format
msgid ""
"multiple files found for the document \"%s\": %s\n"
"Use %r for the build."
msgstr "S'han trobat múltiples fitxers per al document «%s»: %s\nUseu %r per a la compilació."

#: project.py:87
#, python-format
msgid "Ignored unreadable document %r."
msgstr "S'ha ignorat el document il·legible %r."

#: registry.py:167
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "La classe del constructor %s no té cap atribut «name»"

#: registry.py:171
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "El constructor %r ja existeix (al mòdul %s)"

#: registry.py:187
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "El nom del constructor %s no està registrat o disponible a través del punt d'entrada"

#: registry.py:197
#, python-format
msgid "Builder name %s not registered"
msgstr "El nom del constructor %s no està registrat"

#: registry.py:204
#, python-format
msgid "domain %s already registered"
msgstr "el domini %s ja està registrat"

#: registry.py:228 registry.py:249 registry.py:262
#, python-format
msgid "domain %s not yet registered"
msgstr "el domini %s encara no està registrat"

#: registry.py:235
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "La directiva %r ja està registrada al domini %s"

#: registry.py:253
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "El rol %r ja està registrat al domini %s"

#: registry.py:266
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "L'índex %r ja està registrat al domini %s"

#: registry.py:313
#, python-format
msgid "The %r object_type is already registered"
msgstr "L'object_type %r ja està registrat"

#: registry.py:344
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "El crossref_type %r ja està registrat"

#: registry.py:353
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r ja està registrat"

#: registry.py:363
#, python-format
msgid "source_parser for %r is already registered"
msgstr "source_parser per a %r ja està registrat"

#: registry.py:372
#, python-format
msgid "Source parser for %s not registered"
msgstr "L'analitzador de fonts per a %s no registrat"

#: registry.py:390
#, python-format
msgid "Translator for %r already exists"
msgstr "El traductor per a %r ja existeix"

#: registry.py:407
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "kwargs per a add_node() haurà de ser una funció (visita, sortida) tupla: %r=%r"

#: registry.py:496
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r ja està registrat"

#: registry.py:512
#, python-format
msgid "math renderer %s is already registered"
msgstr "la representació matemàtica %s ja està registrada"

#: registry.py:529
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "l'extensió %r ja es va fusionar amb Sphinx des de la versió %s. Aquesta extensió s'ignorarà."

#: registry.py:543
msgid "Original exception:\n"
msgstr "Excepció original:\n"

#: registry.py:545
#, python-format
msgid "Could not import extension %s"
msgstr "No s'ha pogut importar l'extensió %s"

#: registry.py:552
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "l'extensió %r no té cap funció setup(). És realment un mòdul d'extensions de Sphinx?"

#: registry.py:565
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "L'extensió %s usada per aquest projecte almenys necessita Sphinx versió %s i, per tant, no es pot crear amb aquesta versió."

#: registry.py:577
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "l'extensió %r ha retornat un objecte no admès des de la seva funció setup(). No n'hauria de retornar cap o retornar un diccionari de metadades"

#: registry.py:612
#, python-format
msgid "`None` is not a valid filetype for %r."
msgstr "«Cap» no és un tipus de fitxer vàlid per a %r."

#: roles.py:206
#, python-format
msgid "Common Vulnerabilities and Exposures; CVE %s"
msgstr "Vulnerabilitats i exposicions comuns (CVE %s)"

#: roles.py:229
#, python-format
msgid "invalid CVE number %s"
msgstr "número %s de CVE no vàlid"

#: roles.py:251
#, python-format
msgid "Common Weakness Enumeration; CWE %s"
msgstr "Enumeració de les debilitats comuns (CWE %s)"

#: roles.py:274
#, python-format
msgid "invalid CWE number %s"
msgstr "número %s de CWE no vàlid"

#: roles.py:294
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Propostes de millora a Python; PEP %s"

#: roles.py:317
#, python-format
msgid "invalid PEP number %s"
msgstr "número de PEP no vàlid %s"

#: roles.py:355
#, python-format
msgid "invalid RFC number %s"
msgstr "número de RFC no vàlid %s"

#: ext/linkcode.py:86 ext/viewcode.py:226
msgid "[source]"
msgstr "[font]"

#: ext/viewcode.py:289
msgid "highlighting module code... "
msgstr "s'està ressaltant el codi del mòdul... "

#: ext/viewcode.py:320
msgid "[docs]"
msgstr "[documents]"

#: ext/viewcode.py:346
msgid "Module code"
msgstr "Codi del mòdul"

#: ext/viewcode.py:353
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>Codi font per a %s</h1>"

#: ext/viewcode.py:380
msgid "Overview: module code"
msgstr "Vista general: codi del mòdul"

#: ext/viewcode.py:381
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>Tots els mòduls per als quals hi ha codi</h1>"

#: ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr "l'enllaç codificat %r podria substituir-se per un enllaç extern (en el seu lloc intenteu usar %r)"

#: ext/autosectionlabel.py:52
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr "la secció «%s» s'etiqueta com a «%s»"

#: domains/std/__init__.py:833 domains/std/__init__.py:960
#: ext/autosectionlabel.py:61
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "etiqueta duplicada %s, una altra instància a %s"

#: ext/imgmath.py:387 ext/mathjax.py:60
msgid "Link to this equation"
msgstr "Enllaça amb aquesta equació"

#: ext/duration.py:90
msgid ""
"====================== slowest reading durations ======================="
msgstr "==================== durades de lectura més lentes ====================="

#: ext/doctest.py:118
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "manca «+» o «-» a l'opció «%s»."

#: ext/doctest.py:124
#, python-format
msgid "'%s' is not a valid option."
msgstr "«%s» no és una opció vàlida."

#: ext/doctest.py:139
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "«%s» no és una opció pyversion vàlida"

#: ext/doctest.py:226
msgid "invalid TestCode type"
msgstr "tipus de TestCode no vàlid"

#: ext/doctest.py:297
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "Proves de doctests en les fonts acabades, mireu el resultat a %(outdir)s/output.txt."

#: ext/doctest.py:457
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "no hi ha codi/sortida en el bloc %s a %s:%s"

#: ext/doctest.py:568
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "s'ignora el codi doctest no vàlid: %r"

#: ext/imgmath.py:162
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "l'ordre de LaTeX %r no s'ha pogut executar (necessària per a la visualització matemàtica), comproveu la configuració d'«imgmath_latex»"

#: ext/imgmath.py:181
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "%s l'ordre de %r no s'ha pogut executar (necessària per a la visualització matemàtica), comproveu la configuració d'«imgmath_%s»"

#: ext/imgmath.py:344
#, python-format
msgid "display latex %r: %s"
msgstr "visualització de latex %r: %s"

#: ext/imgmath.py:380
#, python-format
msgid "inline latex %r: %s"
msgstr "latex inclòs %r: %s"

#: ext/coverage.py:48
#, python-format
msgid "invalid regex %r in %s"
msgstr "expressions regulars no vàlides %r a %s"

#: ext/coverage.py:140 ext/coverage.py:301
#, python-format
msgid "module %s could not be imported: %s"
msgstr "el mòdul %s no s'ha pogut importar: %s"

#: ext/coverage.py:148
#, python-format
msgid ""
"the following modules are documented but were not specified in "
"coverage_modules: %s"
msgstr "els mòduls següents estan documentats però no s'han especificat a coverage_modules: %s"

#: ext/coverage.py:158
msgid ""
"the following modules are specified in coverage_modules but were not "
"documented"
msgstr "els mòduls següents s'especifiquen a coverage_modules però no estan documentats"

#: ext/coverage.py:172
#, python-brace-format, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)s{sep}python.txt."
msgstr ""

#: ext/coverage.py:187
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "expressions regulars %r no vàlides a coverage_c_regexes"

#: ext/coverage.py:260
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr "API de C sense documentar: %s [ %s] en el fitxer %s"

#: ext/coverage.py:452
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr "funció de Python sense documentar: %s :: %s"

#: ext/coverage.py:473
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr "classe de Python sense documentar: %s :: %s"

#: ext/coverage.py:492
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr "mètode de Python sense documentar: %s :: %s :: %s"

#: ext/imgconverter.py:44
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr "No es pot executar l'ordre de conversió d'imatges %r. «sphinx.ext.imgconverter» requereix de manera predeterminada ImageMagick. Assegureu-vos que està instal·lat o configureu l'opció «image_converter» a una ordre de conversió personalitzada.\n\nTraça: %s"

#: ext/imgconverter.py:56 ext/imgconverter.py:90
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "«convert» ha sortit amb un error:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/imgconverter.py:83
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr "l'ordre «convert» %r no s'ha pogut executar, comproveu la configuració d'«image_converter»"

#: ext/graphviz.py:138
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "La directiva del Graphviz no pot tenir tant el contingut com un argument del nom de fitxer"

#: ext/graphviz.py:153
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "El fitxer extern %r del Graphviz no s'ha trobat ni es pot llegir"

#: ext/graphviz.py:164
msgid "Ignoring \"graphviz\" directive without content."
msgstr "S'està ignorant la directiva «graphviz» sense contingut."

#: ext/graphviz.py:287
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr "S'ha d'establir el camí de l'executable «graphviz_dot»! %r"

#: ext/graphviz.py:328
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "l'ordre «dot» %r no s'ha pogut executar (necessària per a la sortida del Graphviz), comproveu la configuració de «graphviz_dot»"

#: ext/graphviz.py:339
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "«dot» ha sortit amb un error:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:344
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "«dot» no ha produït un fitxer de sortida:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:367
#, python-format
msgid "graphviz_output_format must be either 'png' or 'svg', but is %r"
msgstr ""

#: ext/graphviz.py:373 ext/graphviz.py:436 ext/graphviz.py:480
#, python-format
msgid "dot code %r: %s"
msgstr "codi del «dot» %r: %s"

#: ext/graphviz.py:493 ext/graphviz.py:501
#, python-format
msgid "[graph: %s]"
msgstr "[gràfica: %s]"

#: ext/graphviz.py:495 ext/graphviz.py:503
msgid "[graph]"
msgstr "[gràfica]"

#: ext/todo.py:61
msgid "Todo"
msgstr "Tasca pendent"

#: ext/todo.py:94
#, python-format
msgid "TODO entry found: %s"
msgstr "S'ha trobat una entrada TODO: %s"

#: ext/todo.py:152
msgid "<<original entry>>"
msgstr "<<entrada original>>"

#: ext/todo.py:154
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(L'<<original entry>> es troba a %s, línia %d)."

#: ext/todo.py:166
msgid "original entry"
msgstr "entrada original"

#: directives/code.py:66
msgid "non-whitespace stripped by dedent"
msgstr "cap espai en blanc eliminat en disminuir el sagnat"

#: directives/code.py:87
#, python-format
msgid "Invalid caption: %s"
msgstr "Subtítol no vàlid: %s"

#: directives/code.py:131 directives/code.py:297 directives/code.py:483
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "l'especificació del número de línia queda fora de l'interval (1-%d): %r"

#: directives/code.py:216
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "No es poden usar ambdues opcions «%s» i «%s»"

#: directives/code.py:231
#, python-format
msgid "Include file '%s' not found or reading it failed"
msgstr "No s'ha trobat el fitxer d'inclusió «%s» o n'ha fallat la lectura"

#: directives/code.py:235
#, python-format
msgid ""
"Encoding %r used for reading included file '%s' seems to be wrong, try "
"giving an :encoding: option"
msgstr "La codificació %r emprada per a llegir el fitxer d'inclusió «%s» sembla ser incorrecta, proveu indicant una opció :encoding:"

#: directives/code.py:276
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "L'objecte anomenat %r no es troba en el fitxer inclòs %r"

#: directives/code.py:309
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "No podeu usar «lineno-match» amb un conjunt desarticulat de «línies»"

#: directives/code.py:314
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "Línia específica %r: No hi ha cap línia llançada des del fitxer inclòs %r"

#: directives/patches.py:71
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr "«:file:» l'opció per a la directiva «csv-table» ara reconeix un camí absolut com a camí relatiu des del directori d'origen. Actualitzeu el vostre document."

#: directives/other.py:119
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr "El patró global toctree %r no coincideix amb cap document"

#: directives/other.py:153 environment/adapters/toctree.py:361
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "el toctree conté una referència cap al document exclòs %r"

#: directives/other.py:156
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "el toctree conté una referència cap al document %r, el qual no existeix"

#: directives/other.py:169
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr "s'ha trobat una entrada duplicada en el toctree: %s"

#: directives/other.py:203
msgid "Section author: "
msgstr "Autor de la secció:"

#: directives/other.py:205
msgid "Module author: "
msgstr "Autor del mòdul: "

#: directives/other.py:207
msgid "Code author: "
msgstr "Autor del codi: "

#: directives/other.py:209
msgid "Author: "
msgstr "Autor: "

#: directives/other.py:269
msgid ".. acks content is not a list"
msgstr "... el contingut dels reconeixements no és una llista"

#: directives/other.py:292
msgid ".. hlist content is not a list"
msgstr "... el contingut de l'historial no és una llista"

#: builders/changes.py:29
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "El fitxer de vista general es troba a %(outdir)s."

#: builders/changes.py:56
#, python-format
msgid "no changes in version %s."
msgstr "no hi ha canvis en la versió %s."

#: builders/changes.py:58
msgid "writing summary file..."
msgstr "s'està escrivint el fitxer de vista general..."

#: builders/changes.py:70
msgid "Builtins"
msgstr "Elements incorporats"

#: builders/changes.py:72
msgid "Module level"
msgstr "Nivell de mòdul"

#: builders/changes.py:124
msgid "copying source files..."
msgstr "s'estan copiant els fitxers font..."

#: builders/changes.py:133
#, python-format
msgid "could not read %r for changelog creation"
msgstr "no s'ha pogut llegir %r per a la creació del registre de canvis"

#: builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "Les pàgines del manual es troben a %(outdir)s."

#: builders/manpage.py:45
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "no s'ha trobat el valor de configuració «man_pages»: no s'escriuran les pàgines del manual"

#: builders/latex/__init__.py:347 builders/manpage.py:54
#: builders/singlehtml.py:176 builders/texinfo.py:119
msgid "writing"
msgstr "s'està escrivint"

#: builders/manpage.py:71
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "El valor de configuració «man_pages» fa referència a un document %s desconegut"

#: builders/__init__.py:224
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "no s'ha trobat una imatge adequada per al constructor %s: %s (%s)"

#: builders/__init__.py:232
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "no s'ha trobat una imatge adequada per al constructor %s: %s"

#: builders/__init__.py:255
msgid "building [mo]: "
msgstr "s'estan construint [mo]:"

#: builders/__init__.py:258 builders/__init__.py:759 builders/__init__.py:791
msgid "writing output... "
msgstr "s'està escrivint la sortida..."

#: builders/__init__.py:275
#, python-format
msgid "all of %d po files"
msgstr "tots els %d fitxers PO"

#: builders/__init__.py:297
#, python-format
msgid "targets for %d po files that are specified"
msgstr "objectius per als %d fitxers PO que s'han especificat"

#: builders/__init__.py:309
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "objectius per als %d fitxers PO que estan desfasats"

#: builders/__init__.py:319
msgid "all source files"
msgstr "tots els fitxers font"

#: builders/__init__.py:330
#, python-format
msgid "file %r given on command line does not exist, "
msgstr "el fitxer %r proporcionat a la línia d'ordres no existeix, "

#: builders/__init__.py:337
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "el fitxer %r proporcionat a la línia d'ordres no es troba sota el directori d'origen, s'ignora"

#: builders/__init__.py:348
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr "el fitxer %r proporcionat a la línia d'ordres no és un document vàlid, s'ignora"

#: builders/__init__.py:361
#, python-format
msgid "%d source files given on command line"
msgstr "%d fitxers font proporcionats a la línia d'ordres"

#: builders/__init__.py:377
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "els objectius per a %d fitxers font que estan desfasats"

#: builders/__init__.py:395 builders/gettext.py:265
#, python-format
msgid "building [%s]: "
msgstr "s'està construint [%s]: "

#: builders/__init__.py:406
msgid "looking for now-outdated files... "
msgstr "s'està cercant per fitxers sense actualitzar... "

#: builders/__init__.py:410
#, python-format
msgid "%d found"
msgstr "s'han trobat %d"

#: builders/__init__.py:412
msgid "none found"
msgstr "no se n'ha trobat cap"

#: builders/__init__.py:419
msgid "pickling environment"
msgstr "s'està preparant l'ambient"

#: builders/__init__.py:426
msgid "checking consistency"
msgstr "s'està comprovant la coherència"

#: builders/__init__.py:430
msgid "no targets are out of date."
msgstr "no hi ha cap objectiu desfasat."

#: builders/__init__.py:469
msgid "updating environment: "
msgstr "s'està actualitzant l'entorn: "

#: builders/__init__.py:494
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s afegits, %s canviats, %s eliminats"

#: builders/__init__.py:531
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches a "
"built-in exclude pattern %r. Please move your master document to a different"
" location."
msgstr "L'Sphinx no pot carregar el document mestre (%s) perquè coincideix amb un patró d'exclusió %r a la construcció. Si us plau, moveu el vostre document mestre cap a una altra ubicació."

#: builders/__init__.py:540
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches an "
"exclude pattern specified in conf.py, %r. Please remove this pattern from "
"conf.py."
msgstr "L'Sphinx no pot carregar el document mestre (%s) perquè coincideix amb un patró d'exclusió especificat a «conf.py», %r. Si us plau, elimineu aquest patró."

#: builders/__init__.py:551
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it is not included"
" in the custom include_patterns = %r. Ensure that a pattern in "
"include_patterns matches the master document."
msgstr "L'Sphinx no pot carregar el document mestre (%s) perquè no s'inclou a l'opció personalitzada include_patterns = %r. Assegureu-vos que un patró a include_patterns coincideixi amb el document mestre."

#: builders/__init__.py:558
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s). The master document must "
"be within the source directory or a subdirectory of it."
msgstr "L'Sphinx no pot carregar el document mestre (%s). El document mestre haurà d'estar dins del directori font o un subdirectori d'aquest."

#: builders/__init__.py:576 builders/__init__.py:592
msgid "reading sources... "
msgstr "s'estan llegint les fonts... "

#: builders/__init__.py:713
#, python-format
msgid "docnames to write: %s"
msgstr "els docname que s'escriuran: %s"

#: builders/__init__.py:715
msgid "no docnames to write!"
msgstr ""

#: builders/__init__.py:728
msgid "preparing documents"
msgstr "s'estan preparant els documents"

#: builders/__init__.py:731
msgid "copying assets"
msgstr "s'estan copiant els recursos"

#: builders/__init__.py:883
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "caràcters font no codificables, substituint per «?»: %r"

#: builders/epub3.py:84
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "El fitxer ePub es troba a %(outdir)s."

#: builders/epub3.py:189
msgid "writing nav.xhtml file..."
msgstr "s'està escrivint el fitxer nav.xhtml..."

#: builders/epub3.py:221
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "el valor de configuració «epub_language» (o «language») no pot estar buit per a EPUB3"

#: builders/epub3.py:227
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "el valor de configuració «epub_uid» haurà de ser un XML NAME per a EPUB3"

#: builders/epub3.py:232
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "el valor de configuració «epub_title» (o «html_title») no pot estar buit per a EPUB3"

#: builders/epub3.py:238
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "el valor de configuració «epub_author» no pot estar buit per a EPUB3"

#: builders/epub3.py:242
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "el valor de configuració «epub_contributor» no pot estar buit per a EPUB3"

#: builders/epub3.py:247
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "el valor de configuració «epub_description» no pot estar buit per a EPUB3"

#: builders/epub3.py:251
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "el valor de configuració «epub_publisher» no pot estar buit per a EPUB3"

#: builders/epub3.py:256
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "el valor de configuració «epub_copyright» (o «copyright») no pot estar buit per a EPUB3"

#: builders/epub3.py:262
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "el valor de configuració «epub_identifier» no pot estar buit per a EPUB3"

#: builders/epub3.py:265
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "el valor de configuració «version» no pot estar buit per a EPUB3"

#: builders/epub3.py:279 builders/html/__init__.py:1291
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "css_file no vàlid: %r, s'ignora"

#: builders/xml.py:31
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "Els fitxers en XML es troben a %(outdir)s."

#: builders/html/__init__.py:1241 builders/text.py:76 builders/xml.py:90
#, python-format
msgid "error writing file %s: %s"
msgstr "error en escriure al fitxer %s: %s"

#: builders/xml.py:101
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "Els fitxers en pseudo XML es troben a %(outdir)s."

#: builders/texinfo.py:45
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Els fitxers de Texinfo es troben a %(outdir)s."

#: builders/texinfo.py:48
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nExecuteu l'ordre «make» en aquest directori per a executar-les mitjançant\nel makeinfo (useu l'ordre «make info» per a fer-ho automàticament)."

#: builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "no s'ha trobat el valor de configuració «texinfo_documents»: no s'escriurà cap document"

#: builders/texinfo.py:89
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "El valor de configuració «texinfo_documents» fa referència a un document %s desconegut"

#: builders/latex/__init__.py:325 builders/texinfo.py:113
#, python-format
msgid "processing %s"
msgstr "s'està processant %s"

#: builders/latex/__init__.py:405 builders/texinfo.py:172
msgid "resolving references..."
msgstr "s'estan resolent les referències..."

#: builders/latex/__init__.py:416 builders/texinfo.py:182
msgid " (in "
msgstr " (a "

#: builders/_epub_base.py:422 builders/html/__init__.py:779
#: builders/latex/__init__.py:481 builders/texinfo.py:198
msgid "copying images... "
msgstr "s'estan copiant les imatges... "

#: builders/_epub_base.py:444 builders/latex/__init__.py:496
#: builders/texinfo.py:215
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "no s'ha pogut copiar el fitxer d'imatge %r: %s"

#: builders/texinfo.py:222
msgid "copying Texinfo support files"
msgstr "s'estan copiant els fitxers de suport de Texinfo"

#: builders/texinfo.py:230
#, python-format
msgid "error writing file Makefile: %s"
msgstr "error en escriure el fitxer Makefile: %s"

#: builders/_epub_base.py:223
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "s'ha trobat una entrada ToC duplicada: %s"

#: builders/_epub_base.py:433
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "no s'ha pogut llegir el fitxer d'imatge %r: en el seu lloc, es copia"

#: builders/_epub_base.py:464
#, python-format
msgid "cannot write image file %r: %s"
msgstr "no s'ha pogut escriure el fitxer d'imatge %r: %s"

#: builders/_epub_base.py:476
msgid "Pillow not found - copying image files"
msgstr "No s'ha trobat el Pillow: es copien els fitxers d'imatge"

#: builders/_epub_base.py:511
msgid "writing mimetype file..."
msgstr "s'està escrivint un fitxer de tipus MIME..."

#: builders/_epub_base.py:520
msgid "writing META-INF/container.xml file..."
msgstr "s'està escrivint el fitxer META-INF/container.xml..."

#: builders/_epub_base.py:558
msgid "writing content.opf file..."
msgstr "s'està escrivint el fitxer content.opf..."

#: builders/_epub_base.py:591
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "tipus MIME desconegut per a %s, s'ignora"

#: builders/_epub_base.py:745
msgid "node has an invalid level"
msgstr "el node té un nivell no vàlid"

#: builders/_epub_base.py:765
msgid "writing toc.ncx file..."
msgstr "s'està escrivint el fitxer toc.ncx..."

#: builders/_epub_base.py:794
#, python-format
msgid "writing %s file..."
msgstr "s'està escrivint el fitxer %s..."

#: builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "El constructor fictici no genera cap fitxer."

#: builders/gettext.py:244
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "Els catàlegs de missatges es troben a %(outdir)s."

#: builders/gettext.py:266
#, python-format
msgid "targets for %d template files"
msgstr "objectius per a %d fitxers de plantilla"

#: builders/gettext.py:271
msgid "reading templates... "
msgstr "s'estan llegint les plantilles... "

#: builders/gettext.py:307
msgid "writing message catalogs... "
msgstr "s'estan escrivint els catàlegs de missatges... "

#: builders/singlehtml.py:35
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "La pàgina HTML es troba a %(outdir)s."

#: builders/singlehtml.py:171
msgid "assembling single document"
msgstr "s'està muntant un únic document"

#: builders/singlehtml.py:189
msgid "writing additional files"
msgstr "s'estan escrivint els fitxers addicionals"

#: builders/linkcheck.py:77
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "Cerqueu qualsevol error a la sortida anterior o en el fitxer %(outdir)s/output.txt"

#: builders/linkcheck.py:149
#, python-format
msgid "broken link: %s (%s)"
msgstr "enllaç trencat: %s (%s)"

#: builders/linkcheck.py:548
#, python-format
msgid "Anchor '%s' not found"
msgstr "No s'ha trobat l'àncora «%s»"

#: builders/linkcheck.py:758
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr "Ha fallat en compilar expressions regulars a linkcheck_allowed_redirects: %r %s"

#: builders/text.py:29
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "Els fitxers de text es troben a %(outdir)s."

#: transforms/i18n.py:227 transforms/i18n.py:302
#, python-brace-format
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "referències incoherents de nota al peu en el missatge traduït. Original: {0}, traduït: {1}"

#: transforms/i18n.py:272
#, python-brace-format
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "referències incoherents en el missatge traduït. Original: {0}, traduït: {1}"

#: transforms/i18n.py:322
#, python-brace-format
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "referències incoherents de citació en el missatge traduït. Original: {0}, traduït: {1}"

#: transforms/i18n.py:344
#, python-brace-format
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "referències incoherents de terme en el missatge traduït. Original: {0}, traduït: {1}"

#: builders/html/__init__.py:486 builders/latex/__init__.py:199
#: transforms/__init__.py:129 writers/manpage.py:98 writers/texinfo.py:220
#, python-format
msgid "%b %d, %Y"
msgstr "%-d %b, %Y"

#: transforms/__init__.py:139
msgid "could not calculate translation progress!"
msgstr "no s'ha pogut calcular el progrés de la traducció!"

#: transforms/__init__.py:144
msgid "no translated elements!"
msgstr "No hi ha cap element traduït!"

#: transforms/__init__.py:253
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "S'ha trobat un índex basat en 4 columnes. Pot ser un error de les extensions que utilitzeu: %r"

#: transforms/__init__.py:294
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "La nota al peu [%s] no té una referència."

#: transforms/__init__.py:303
msgid "Footnote [*] is not referenced."
msgstr "No es fa referència a la nota al peu [*]."

#: transforms/__init__.py:314
msgid "Footnote [#] is not referenced."
msgstr "La nota al peu [núm.] no té una referència."

#: _cli/__init__.py:73
msgid "Usage:"
msgstr "Ús:"

#: _cli/__init__.py:75
#, python-brace-format
msgid "{0} [OPTIONS] <COMMAND> [<ARGS>]"
msgstr "{0} [OPCIONS] <COMMAND> [<ARGS>]"

#: _cli/__init__.py:78
msgid "  The Sphinx documentation generator."
msgstr " El generador de documentació Sphinx."

#: _cli/__init__.py:87
msgid "Commands:"
msgstr "Ordres:"

#: _cli/__init__.py:98
msgid "Options"
msgstr "Opcions"

#: _cli/__init__.py:113 _cli/__init__.py:181
msgid "For more information, visit https://www.sphinx-doc.org/en/master/man/."
msgstr "Per a més informació, visiteu https://www.sphinx-doc.org/en/master/man/."

#: _cli/__init__.py:171
#, python-brace-format
msgid ""
"{0}: error: {1}\n"
"Run '{0} --help' for information"
msgstr "{0}: error: {1}\nPer a informació executeu «{0} --help»"

#: _cli/__init__.py:179
msgid "   Manage documentation with Sphinx."
msgstr "   Gestiona la documentació amb Sphinx."

#: _cli/__init__.py:191
msgid "Show the version and exit."
msgstr "Mostra la versió i surt."

#: _cli/__init__.py:199
msgid "Show this message and exit."
msgstr "Mostra aquest missatge i surt."

#: _cli/__init__.py:203
msgid "Logging"
msgstr "Registre"

#: _cli/__init__.py:210
msgid "Increase verbosity (can be repeated)"
msgstr "Augmenta la verbositat (es pot repetir)"

#: _cli/__init__.py:218
msgid "Only print errors and warnings."
msgstr "Imprimeix només els errors i els avisos."

#: _cli/__init__.py:225
msgid "No output at all"
msgstr "Cap sortida en absolut"

#: _cli/__init__.py:231
msgid "<command>"
msgstr "<command>"

#: _cli/__init__.py:263
msgid "See 'sphinx --help'.\n"
msgstr "Vegeu «sphinx --help».\n"

#: environment/__init__.py:86
msgid "new config"
msgstr "configuració nova"

#: environment/__init__.py:87
msgid "config changed"
msgstr "configuració modificada"

#: environment/__init__.py:88
msgid "extensions changed"
msgstr "extensions modificades"

#: environment/__init__.py:253
msgid "build environment version not current"
msgstr "la versió de l'entorn de compilació no és actual"

#: environment/__init__.py:255
msgid "source directory has changed"
msgstr "el directori d'origen ha estat modificat"

#: environment/__init__.py:325
#, python-format
msgid "The configuration has changed (1 option: %r)"
msgstr "La configuració ha estat canviada (1 opció: %r)"

#: environment/__init__.py:330
#, python-format
msgid "The configuration has changed (%d options: %s)"
msgstr "La configuració ha estat canviada (%d opcions: %s)"

#: environment/__init__.py:336
#, python-format
msgid "The configuration has changed (%d options: %s, ...)"
msgstr "La configuració ha estat canviada (%d opcions: %s, «...»)"

#: environment/__init__.py:379
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "Aquest entorn és incompatible amb el constructor seleccionat, trieu un altre directori doctree."

#: environment/__init__.py:493
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "Ha fallat en escanejar els documents a %s: %r"

#: environment/__init__.py:658 ext/intersphinx/_resolve.py:234
#, python-format
msgid "Domain %r is not registered"
msgstr "El domini %r no està registrat"

#: environment/__init__.py:813
msgid "document isn't included in any toctree"
msgstr "el document no està inclòs en cap toctree"

#: environment/__init__.py:859
msgid "self referenced toctree found. Ignored."
msgstr "S'ha trobat un toctree autoreferenciat. S'ignora."

#: environment/__init__.py:889
#, python-format
msgid "document is referenced in multiple toctrees: %s, selecting: %s <- %s"
msgstr "es fa referència al document en múltiples toctree: %s, se selecciona: %s <- %s"

#: util/i18n.py:100
#, python-format
msgid "reading error: %s, %s"
msgstr "error de lectura: %s, %s"

#: util/i18n.py:113
#, python-format
msgid "writing error: %s, %s"
msgstr "error d'escriptura: %s, %s"

#: util/i18n.py:146
#, python-format
msgid "locale_dir %s does not exist"
msgstr "locale_dir %s no existeix"

#: util/i18n.py:236
#, python-format
msgid "Invalid Babel locale: %r."
msgstr ""

#: util/i18n.py:245
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "Format de data no vàlid. Citeu la cadena amb cometes senzilles si voleu generar-la directament: %s"

#: util/docfields.py:103
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr "Problema en el domini %s: se suposa que el camp usa el rol «%s», però no es troba en el domini."

#: util/nodes.py:423
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr "%r està en desús per a les entrades d'índex (des de l'entrada %r). En el seu lloc useu «pair: %s»."

#: util/nodes.py:490
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "el toctree conté una referència cap al fitxer %r que no existeix"

#: util/nodes.py:706
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "excepció mentre només s'avaluava l'expressió directiva: %s"

#: util/display.py:82
msgid "skipped"
msgstr "s'omet"

#: util/display.py:87
msgid "failed"
msgstr "ha fallat"

#: util/osutil.py:131
#, python-format
msgid ""
"Aborted attempted copy from %s to %s (the destination path has existing "
"data)."
msgstr "S'ha interromput l'intent de còpia des de %s a %s (hi ha dades existents al camí de destinació)."

#: util/docutils.py:309
#, python-format
msgid "unknown directive name: %s"
msgstr ""

#: util/docutils.py:345
#, python-format
msgid "unknown role name: %s"
msgstr ""

#: util/docutils.py:789
#, python-format
msgid "unknown node type: %r"
msgstr "tipus de node desconegut: %r"

#: util/fileutil.py:76
#, python-format
msgid ""
"Aborted attempted copy from rendered template %s to %s (the destination path"
" has existing data)."
msgstr "S'ha interromput l'intent de còpia des de la plantilla representada %s a %s (hi ha dades existents al camí de destinació)."

#: util/fileutil.py:89
#, python-format
msgid "Writing evaluated template result to %s"
msgstr "Escriu el resultat de la plantilla avaluada a %s"

#: util/rst.py:73
#, python-format
msgid "default role %s not found"
msgstr "no s'ha trobat el rol predeterminat %s"

#: util/inventory.py:147
#, python-format
msgid "inventory <%s> contains duplicate definitions of %s"
msgstr "l'inventari <%s> conté definicions duplicades de %s"

#: util/inventory.py:166
#, python-format
msgid "inventory <%s> contains multiple definitions for %s"
msgstr "l'inventari <%s> conté múltiples definicions per a %s"

#: writers/latex.py:1097 writers/manpage.py:259 writers/texinfo.py:663
msgid "Footnotes"
msgstr "Notes al peu"

#: writers/manpage.py:289 writers/text.py:945
#, python-format
msgid "[image: %s]"
msgstr "[imatge: %s]"

#: writers/manpage.py:290 writers/text.py:946
msgid "[image]"
msgstr "[imatge]"

#: builders/latex/__init__.py:206 domains/std/__init__.py:771
#: domains/std/__init__.py:784 templates/latex/latex.tex.jinja:106
#: themes/basic/genindex-single.html:22 themes/basic/genindex-single.html:48
#: themes/basic/genindex-split.html:3 themes/basic/genindex-split.html:6
#: themes/basic/genindex.html:3 themes/basic/genindex.html:26
#: themes/basic/genindex.html:59 themes/basic/layout.html:127
#: writers/texinfo.py:514
msgid "Index"
msgstr "Índex"

#: writers/latex.py:743 writers/texinfo.py:646
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "el node del títol no s'ha trobat en la secció, tema, taula, advertiment o nota al marge"

#: writers/texinfo.py:1217
msgid "caption not inside a figure."
msgstr "el subtítol no es troba dins d'una figura."

#: writers/texinfo.py:1303
#, python-format
msgid "unimplemented node type: %r"
msgstr "tipus de node sense implementar: %r"

#: writers/latex.py:361
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "%r toplevel_sectioning desconegut per a la classe %r"

#: builders/latex/__init__.py:224 writers/latex.py:411
#, python-format
msgid "no Babel option known for language %r"
msgstr "no es coneix l'opció de Babel per a l'idioma %r"

#: writers/latex.py:429
msgid "too large :maxdepth:, ignored."
msgstr ":maxdepth: massa gran, s'ignora."

#: writers/latex.py:591
#, python-format
msgid "template %s not found; loading from legacy %s instead"
msgstr "no s'ha trobat la plantilla %s. En el seu lloc s'està carregant des del llegat %s"

#: writers/latex.py:707
msgid "document title is not a single Text node"
msgstr "el títol del document no és només un node de text"

#: writers/html5.py:572 writers/latex.py:1106
#, python-format
msgid "unsupported rubric heading level: %s"
msgstr "nivell de capçalera de rúbrica no compatible: %s"

#: writers/latex.py:1183
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "s'indiquen tant les columnes tabulars com l'opció «:widths:». S'ignora l'opció «:widths:»."

#: writers/latex.py:1580
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "la unitat de dimensió %s no és vàlida. S'ignora."

#: writers/latex.py:1939
#, python-format
msgid "unknown index entry type %s found"
msgstr "s'ha trobat el tipus d'entrada %s amb un índex desconegut"

#: domains/math.py:128 writers/latex.py:2495
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "math_eqref_format no vàlid: %r"

#: writers/html5.py:96 writers/html5.py:105
msgid "Link to this definition"
msgstr "Enllaça amb aquesta definició"

#: writers/html5.py:431
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "numfig_format no s'ha definit per a %s"

#: writers/html5.py:441
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "Qualsevol ID no assignat per al node %s"

#: writers/html5.py:496
msgid "Link to this term"
msgstr "Enllaça amb aquest terme"

#: writers/html5.py:548 writers/html5.py:553
msgid "Link to this heading"
msgstr "Enllaça amb aquesta capçalera"

#: writers/html5.py:558
msgid "Link to this table"
msgstr "Enllaça amb aquesta taula"

#: writers/html5.py:636
msgid "Link to this code"
msgstr "Enllaça amb aquest codi"

#: writers/html5.py:638
msgid "Link to this image"
msgstr "Enllaça amb aquesta imatge"

#: writers/html5.py:640
msgid "Link to this toctree"
msgstr "Enllaça amb aquest toctree"

#: writers/html5.py:766
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "No s'ha pogut obtenir la mida de la imatge. S'ignora l'opció «:scale:»."

#: domains/__init__.py:322
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: domains/math.py:73
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "etiqueta duplicada de l'equació %s, una altra instància a %s"

#: domains/javascript.py:182
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (funció interna)"

#: domains/javascript.py:183 domains/python/__init__.py:287
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (mètode %s)"

#: domains/javascript.py:185
#, python-format
msgid "%s() (class)"
msgstr "%s() (classe)"

#: domains/javascript.py:187
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (variable global o constant)"

#: domains/javascript.py:189 domains/python/__init__.py:378
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (atribut %s)"

#: domains/javascript.py:273
msgid "Arguments"
msgstr "Arguments"

#: domains/cpp/__init__.py:489 domains/javascript.py:280
msgid "Throws"
msgstr "Llançaments"

#: domains/c/__init__.py:339 domains/cpp/__init__.py:502
#: domains/javascript.py:287 domains/python/_object.py:221
msgid "Returns"
msgstr "Retorna"

#: domains/c/__init__.py:345 domains/javascript.py:293
#: domains/python/_object.py:227
msgid "Return type"
msgstr "Tipus de retorn"

#: domains/javascript.py:370
#, python-format
msgid "%s (module)"
msgstr "%s (mòdul)"

#: domains/c/__init__.py:751 domains/cpp/__init__.py:941
#: domains/javascript.py:415 domains/python/__init__.py:740
msgid "function"
msgstr "funció"

#: domains/javascript.py:416 domains/python/__init__.py:744
msgid "method"
msgstr "mètode"

#: domains/cpp/__init__.py:939 domains/javascript.py:417
#: domains/python/__init__.py:742
msgid "class"
msgstr "classe"

#: domains/javascript.py:418 domains/python/__init__.py:741
msgid "data"
msgstr "dades"

#: domains/javascript.py:419 domains/python/__init__.py:747
msgid "attribute"
msgstr "atribut"

#: domains/javascript.py:420 domains/python/__init__.py:750
msgid "module"
msgstr "mòdul"

#: domains/javascript.py:454
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr "descripció %s duplicada de %s, una altra %s a %s"

#: domains/changeset.py:26
#, python-format
msgid "Added in version %s"
msgstr "Afegit a la versió %s"

#: domains/changeset.py:27
#, python-format
msgid "Changed in version %s"
msgstr "Canviat a la versió %s"

#: domains/changeset.py:28
#, python-format
msgid "Deprecated since version %s"
msgstr "Obsolet des de la versió %s"

#: domains/changeset.py:29
#, python-format
msgid "Removed in version %s"
msgstr "S'ha eliminat a la versió %s"

#: domains/rst.py:131 domains/rst.py:190
#, python-format
msgid "%s (directive)"
msgstr "%s (directiva)"

#: domains/rst.py:191 domains/rst.py:202
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: (opció de la directiva)"

#: domains/rst.py:224
#, python-format
msgid "%s (role)"
msgstr "%s (rol)"

#: domains/rst.py:234
msgid "directive"
msgstr "directiva"

#: domains/rst.py:235
msgid "directive-option"
msgstr "opció_de_la_directiva"

#: domains/rst.py:236
msgid "role"
msgstr "rol"

#: domains/rst.py:262
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "descripció duplicada del %s %s, una altra instància a %s"

#: domains/citation.py:75
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "citació duplicada %s, una altra instància a %s"

#: domains/citation.py:92
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "No es fa referència a la citació [%s]."

#: locale/__init__.py:228
msgid "Attention"
msgstr "Atenció"

#: locale/__init__.py:229
msgid "Caution"
msgstr "Compte"

#: locale/__init__.py:230
msgid "Danger"
msgstr "Perill"

#: locale/__init__.py:231
msgid "Error"
msgstr "Error"

#: locale/__init__.py:232
msgid "Hint"
msgstr "Suggeriment"

#: locale/__init__.py:233
msgid "Important"
msgstr "Important"

#: locale/__init__.py:234
msgid "Note"
msgstr "Nota"

#: locale/__init__.py:235
msgid "See also"
msgstr "Vegeu també"

#: locale/__init__.py:236
msgid "Tip"
msgstr "Truc"

#: locale/__init__.py:237
msgid "Warning"
msgstr "Avís"

#: cmd/quickstart.py:52
msgid "automatically insert docstrings from modules"
msgstr "insereix automàticament les docstring des dels mòduls"

#: cmd/quickstart.py:53
msgid "automatically test code snippets in doctest blocks"
msgstr "prova automàticament els fragments de codi en els blocs doctest"

#: cmd/quickstart.py:54
msgid "link between Sphinx documentation of different projects"
msgstr "enllaç entre la documentació de Sphinx de projectes diferents"

#: cmd/quickstart.py:55
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "escriu les entrades «todo» que es poden mostrar o ocultar durant la construcció"

#: cmd/quickstart.py:56
msgid "checks for documentation coverage"
msgstr "comprovacions per a la cobertura de la documentació"

#: cmd/quickstart.py:57
msgid "include math, rendered as PNG or SVG images"
msgstr "inclou expressions matemàtiques, mostrades com a imatges PNG o SVG"

#: cmd/quickstart.py:58
msgid "include math, rendered in the browser by MathJax"
msgstr "inclou expressions matemàtiques, representades en el navegador per MathJax"

#: cmd/quickstart.py:59
msgid "conditional inclusion of content based on config values"
msgstr "inclusió condicional de contingut basat en els valors de la configuració"

#: cmd/quickstart.py:60
msgid "include links to the source code of documented Python objects"
msgstr "inclou els enllaços cap al codi font dels objectes documentats en Python"

#: cmd/quickstart.py:61
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "crea un fitxer .nojekyll per a publicar el document a les pàgines de GitHub"

#: cmd/quickstart.py:110
msgid "Please enter a valid path name."
msgstr "Si us plau, introduïu un nom de camí vàlid."

#: cmd/quickstart.py:126
msgid "Please enter some text."
msgstr "Si us plau, introduïu algun text."

#: cmd/quickstart.py:133
#, python-format
msgid "Please enter one of %s."
msgstr "Si us plau, introduïu un dels %s."

#: cmd/quickstart.py:141
msgid "Please enter either 'y' or 'n'."
msgstr "Si us plau, introduïu qualsevol de «y» o «n»."

#: cmd/quickstart.py:147
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "Si us plau, introduïu un sufix de fitxer, p. ex., «.rst» o «.txt»."

#: cmd/quickstart.py:229
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Us donem la benvinguda a la utilitat d'inici ràpid de Sphinx %s."

#: cmd/quickstart.py:234
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "Introduïu els valors per a les configuracions següents (només premeu «Retorn»\nper a acceptar un valor predeterminat, si se'n dona un entre parèntesis)."

#: cmd/quickstart.py:241
#, python-format
msgid "Selected root path: %s"
msgstr "Camí arrel seleccionat: %s"

#: cmd/quickstart.py:244
msgid "Enter the root path for documentation."
msgstr "Introduïu el camí arrel per a la documentació."

#: cmd/quickstart.py:245
msgid "Root path for the documentation"
msgstr "Camí arrel per a la documentació"

#: cmd/quickstart.py:254
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "Error: ja existeix un fitxer conf.py en el camí arrel seleccionat."

#: cmd/quickstart.py:259
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "«sphinx-quickstart» no sobreescriurà els projectes de Sphinx existents."

#: cmd/quickstart.py:262
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "Introduïu un camí arrel nou (o premeu «Retorn» per a sortir)"

#: cmd/quickstart.py:273
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "Teniu dues opcions per a col·locar el directori de construcció per a la\nsortida de Sphinx. O useu un directori «_build» dins del camí arrel,\no els directoris separats «source» i «build» dins del camí arrel."

#: cmd/quickstart.py:279
msgid "Separate source and build directories (y/n)"
msgstr "Separa els directoris «source» i «build» (s/n)"

#: cmd/quickstart.py:286
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "Dins del directori arrel, es crearan dos directoris més: «_templates» per a\nles plantilles HTML personalitzades i «_static» per als fulls d'estil\npersonalitzats i altres fitxers estàtics. Podeu introduir un altre prefix\n(com «.») per a substituir el guió baix."

#: cmd/quickstart.py:291
msgid "Name prefix for templates and static dir"
msgstr "Prefix de nom per als directoris «templates» i «static»"

#: cmd/quickstart.py:297
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "El nom del projecte apareixerà en diversos llocs de la documentació construïda."

#: cmd/quickstart.py:300
msgid "Project name"
msgstr "Nom del projecte"

#: cmd/quickstart.py:302
msgid "Author name(s)"
msgstr "Noms de l'autoria"

#: cmd/quickstart.py:308
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr "Sphinx té la noció d'una «versió» i un «llançament» per al programari.\nCada versió pot tenir múltiples versions. Per exemple, per a Python,\nla versió és una cosa semblant a 2.5 o 3.0, mentre que el llançament és\ncom 2.5.1 o 3.0a1. Si no necessiteu aquesta doble estructura, senzillament\nestabliu ambdues amb el mateix valor."

#: cmd/quickstart.py:315
msgid "Project version"
msgstr "Versió del projecte"

#: cmd/quickstart.py:317
msgid "Project release"
msgstr "Llançament del projecte"

#: cmd/quickstart.py:323
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "Si els documents s'han d'escriure en un idioma que no sigui l'anglès,\npodeu seleccionar un idioma aquí per al vostre codi d'idioma.\nA continuació, Sphinx traduirà el text que es genera en aquest idioma.\n\nPer a obtenir una llista dels codis admesos, vegeu\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."

#: cmd/quickstart.py:331
msgid "Project language"
msgstr "Idioma del projecte"

#: cmd/quickstart.py:339
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr "El sufix del nom del fitxer per als fitxers d'origen. Normalment, aquest és\n«.txt» o «.rst». Només els fitxers amb aquest sufix es consideraran documents."

#: cmd/quickstart.py:343
msgid "Source file suffix"
msgstr "Sufix del fitxer font"

#: cmd/quickstart.py:349
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr "Un document és especial perquè es considera el node superior de l'«arbre de\ncontingut», és a dir, és l'arrel de l'estructura jeràrquica dels documents.\nNormalment, es tracta de l'«index», però si el document «index» és una\nplantilla personalitzada, també podreu establir-la a un altre nom de fitxer."

#: cmd/quickstart.py:356
msgid "Name of your master document (without suffix)"
msgstr "Nom del document mestre (sense sufix)"

#: cmd/quickstart.py:367
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "Error: el fitxer mestre %s ja es troba en el camí arrel seleccionat."

#: cmd/quickstart.py:373
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "«sphinx-quickstart» no sobreescriurà el fitxer existent."

#: cmd/quickstart.py:377
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "Introduïu un nom de fitxer nou o canvieu-ne el nom i premeu «Retorn»"

#: cmd/quickstart.py:385
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "Indiqueu quines de les extensions següents de Sphinx haurien d'estar habilitades:"

#: cmd/quickstart.py:396
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "Nota: «imgmath» i «mathjax» no es poden habilitar alhora. «imgmath» ha estat desseleccionat."

#: cmd/quickstart.py:406
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr "Es pot generar un fitxer Makefile i un fitxer d'ordres de Windows,\nde manera que només haureu d'executar, p. ex., «make html»\nen lloc d'invocar directament «sphinx-build»."

#: cmd/quickstart.py:411
msgid "Create Makefile? (y/n)"
msgstr "Voleu crear el Makefile? (s/n)"

#: cmd/quickstart.py:415
msgid "Create Windows command file? (y/n)"
msgstr "Voleu crear el fitxer d'ordres de Windows? (s/n)"

#: cmd/quickstart.py:467 ext/apidoc/_generate.py:76
#, python-format
msgid "Creating file %s."
msgstr "S'està creant el fitxer %s."

#: cmd/quickstart.py:472 ext/apidoc/_generate.py:73
#, python-format
msgid "File %s already exists, skipping."
msgstr "El fitxer %s ja existeix, se salta."

#: cmd/quickstart.py:515
msgid "Finished: An initial directory structure has been created."
msgstr "Finalitzat: s'ha creat una estructura inicial del directori."

#: cmd/quickstart.py:519
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr "Ara heu de completar el fitxer mestre %s i crear altres fitxers font de documentació. "

#: cmd/quickstart.py:526
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr "Useu el Makefile per a construir els documents, com segueix:\n   make builder"

#: cmd/quickstart.py:530
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr "Useu l'ordre «sphinx-build» per a construir els documents, com segueix:\n   sphinx-build -b constructor %s %s"

#: cmd/quickstart.py:537
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "on «constructor» és un dels constructors admesos, p. ex., html, latex o linkcheck."

#: cmd/quickstart.py:572
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nGenereu els fitxers necessaris per a un projecte Sphinx.\n\n«sphinx-quickstart» és una eina interactiva que fa algunes preguntes sobre el\nprojecte i després genera un directori complet de documentació i un\nexemple del fitxer Makefile per a ser usat amb l'ordre «sphinx-build».\n"

#: cmd/build.py:73 cmd/quickstart.py:581 ext/apidoc/_cli.py:27
#: ext/autosummary/generate.py:835
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr "Per a més informació, visiteu <https://www.sphinx-doc.org/>."

#: cmd/quickstart.py:591
msgid "quiet mode"
msgstr "mode silenciós"

#: cmd/quickstart.py:601
msgid "project root"
msgstr "arrel del projecte"

#: cmd/quickstart.py:604
msgid "Structure options"
msgstr "Opcions de l'estructura"

#: cmd/quickstart.py:610
msgid "if specified, separate source and build dirs"
msgstr "Si s'especifica, se separarà el codi font i els directoris de compilació"

#: cmd/quickstart.py:616
msgid "if specified, create build dir under source dir"
msgstr "Si s'especifica, es crearà el directori de construcció a dins del directori d'origen"

#: cmd/quickstart.py:622
msgid "replacement for dot in _templates etc."
msgstr "substitució per a punts a _templates, etc."

#: cmd/quickstart.py:625
msgid "Project basic options"
msgstr "Opcions bàsiques del projecte"

#: cmd/quickstart.py:627
msgid "project name"
msgstr "nom del projecte"

#: cmd/quickstart.py:630
msgid "author names"
msgstr "noms de l'autoria"

#: cmd/quickstart.py:637
msgid "version of project"
msgstr "versió del projecte"

#: cmd/quickstart.py:644
msgid "release of project"
msgstr "llançament del projecte"

#: cmd/quickstart.py:651
msgid "document language"
msgstr "idioma del document"

#: cmd/quickstart.py:654
msgid "source file suffix"
msgstr "sufix del fitxer font"

#: cmd/quickstart.py:657
msgid "master document name"
msgstr "nom del document mestre"

#: cmd/quickstart.py:660
msgid "use epub"
msgstr "usa epub"

#: cmd/quickstart.py:663
msgid "Extension options"
msgstr "Opcions de l'extensió"

#: cmd/quickstart.py:670
#, python-format
msgid "enable %s extension"
msgstr "habilita l'extensió %s"

#: cmd/quickstart.py:677
msgid "enable arbitrary extensions"
msgstr "habilita les extensions arbitràries"

#: cmd/quickstart.py:680
msgid "Makefile and Batchfile creation"
msgstr "Creació dels fitxers Makefile i de processament per lots"

#: cmd/quickstart.py:686
msgid "create makefile"
msgstr "es crea el Makefile"

#: cmd/quickstart.py:692
msgid "do not create makefile"
msgstr "no es crea el Makefile"

#: cmd/quickstart.py:699
msgid "create batchfile"
msgstr "es crea el fitxer de processament per lots"

#: cmd/quickstart.py:705
msgid "do not create batchfile"
msgstr "no es crea el fitxer de processament per lots"

#: cmd/quickstart.py:714
msgid "use make-mode for Makefile/make.bat"
msgstr "usa el mode make per a Makefile/make.bat"

#: cmd/quickstart.py:717 ext/apidoc/_cli.py:243
msgid "Project templating"
msgstr "Plantilles de projecte"

#: cmd/quickstart.py:723 ext/apidoc/_cli.py:249
msgid "template directory for template files"
msgstr "directori de plantilles per als fitxers de plantilla"

#: cmd/quickstart.py:730
msgid "define a template variable"
msgstr "defineix una variable de plantilla"

#: cmd/quickstart.py:766
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "s'especifica «quiet», però no s'especifica cap «project» o «author»."

#: cmd/quickstart.py:785
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "Error: el camí especificat no és un directori o ja hi ha els fitxers de Sphinx."

#: cmd/quickstart.py:792
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "«sphinx-quickstart» només generarà dins d'un directori buit. Especifiqueu un camí arrel nou."

#: cmd/quickstart.py:809
#, python-format
msgid "Invalid template variable: %s"
msgstr "Variable no vàlida de plantilla: %s"

#: cmd/build.py:64
msgid "job number should be a positive number"
msgstr "el número de treball hauria de ser un nombre positiu"

#: cmd/build.py:74
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr "\nGenerar la documentació a partir dels fitxers font.\n\nL'eina «sphinx-build» generarà la documentació a partir dels fitxers\na SOURCEDIR i els situarà a OUTPUTDIR. Cerqueu el «conf.py» en el\nSOURCEDIR per als paràmetres de configuració. L'eina «sphinx-quickstart» es pot usar per a generar fitxers de plantilla, inclòs el «conf.py».\n\nL'eina «sphinx-build» pot crear documentació en formats diferents. A la\nlínia d'ordres se selecciona un format que especifica el nom del constructor.\nDe manera predeterminada és HTML. Els constructors també poden dur a terme\naltres tasques relacionades amb el processament de la documentació.\n\nDe manera predeterminada, es construeix tot el que està desactualitzat. Es pot\ngenerar la sortida només per als fitxers seleccionats especificant noms de fitxer\nindividuals.\n"

#: cmd/build.py:100
msgid "path to documentation source files"
msgstr "camí cap als fitxers font de la documentació"

#: cmd/build.py:103
msgid "path to output directory"
msgstr "camí cap al directori de sortida"

#: cmd/build.py:109
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr "(opcional) una llista de fitxers específics que s'han de reconstruir. S'ignorarà si s'especifica «--write-all»"

#: cmd/build.py:114
msgid "general options"
msgstr "opcions generals"

#: cmd/build.py:121
msgid "builder to use (default: 'html')"
msgstr "constructor que s'usarà (predeterminat: «html»)"

#: cmd/build.py:131
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr "executa en paral·lel amb N processos, quan sigui possible. «auto» uxa el nombre de nuclis de la CPU"

#: cmd/build.py:140
msgid "write all files (default: only write new and changed files)"
msgstr "escriu tots els fitxers (predeterminat: només escriu els fitxers nous i els que han canviat)"

#: cmd/build.py:147
msgid "don't use a saved environment, always read all files"
msgstr "no usar un entorn desat, llegeix sempre tots els fitxers"

#: cmd/build.py:150
msgid "path options"
msgstr "opcions de camí"

#: cmd/build.py:157
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr "directori per als fitxers doctree i d'entorn (predeterminat: OUTPUT_DIR/.doctrees)"

#: cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr "directori per al fitxer de configuració (conf.py) (predeterminat: SOURCE_DIR)"

#: cmd/build.py:175
msgid "use no configuration file, only use settings from -D options"
msgstr "no usa cap fitxer de configuració, només usa la configuració de les opcions de «-D»"

#: cmd/build.py:184
msgid "override a setting in configuration file"
msgstr "superposa una configuració en el fitxer de configuració"

#: cmd/build.py:193
msgid "pass a value into HTML templates"
msgstr "passa un valor a dins de les plantilles HTML"

#: cmd/build.py:202
msgid "define tag: include \"only\" blocks with TAG"
msgstr "defineix l'etiqueta: inclou blocs «only» amb TAG"

#: cmd/build.py:209
msgid "nitpicky mode: warn about all missing references"
msgstr "mode primmirat: avisa sobre totes les referències que manquen"

#: cmd/build.py:212
msgid "console output options"
msgstr "opcions de sortida de la consola"

#: cmd/build.py:219
msgid "increase verbosity (can be repeated)"
msgstr "augmenta la loquacitat (es pot repetir)"

#: cmd/build.py:226 ext/apidoc/_cli.py:66
msgid "no output on stdout, just warnings on stderr"
msgstr "sense sortida a la sortida estàndard, només avisos a la sortida d'error estàndard"

#: cmd/build.py:233
msgid "no output at all, not even warnings"
msgstr "sense sortida, ni tan sols els avisos"

#: cmd/build.py:241
msgid "do emit colored output (default: auto-detect)"
msgstr "emet una sortida amb colors (predeterminada: detecció automàtica)"

#: cmd/build.py:249
msgid "do not emit colored output (default: auto-detect)"
msgstr "no emetre una sortida amb colors (predeterminada: detecció automàtica)"

#: cmd/build.py:252
msgid "warning control options"
msgstr "opcions de control d'avís"

#: cmd/build.py:258
msgid "write warnings (and errors) to given file"
msgstr "escriviu els avisos (i errors) al fitxer indicat"

#: cmd/build.py:265
msgid "turn warnings into errors"
msgstr "converteix els avisos en errors"

#: cmd/build.py:273
msgid "show full traceback on exception"
msgstr "mostra la traça completa en excepció"

#: cmd/build.py:276
msgid "run Pdb on exception"
msgstr "executa Pdb en excepció"

#: cmd/build.py:282
msgid "raise an exception on warnings"
msgstr "planteja una excepció sobre els avisos"

#: cmd/build.py:325
msgid "cannot combine -a option and filenames"
msgstr "no es pot combinar l'opció -a i els noms de fitxer"

#: cmd/build.py:357
#, python-format
msgid "cannot open warning file '%s': %s"
msgstr "no es pot obrir el fitxer de l'avís «%s»: %s"

#: cmd/build.py:376
msgid "-D option argument must be in the form name=value"
msgstr "l'argument de l'opció «-D» haurà d'estar en la forma «nom=valor»"

#: cmd/build.py:383
msgid "-A option argument must be in the form name=value"
msgstr "l'argument de l'opció -A haurà d'estar en la forma «nom=valor»"

#: themes/classic/layout.html:12 themes/classic/static/sidebar.js.jinja:51
msgid "Collapse sidebar"
msgstr "Redueix la barra lateral"

#: themes/agogo/layout.html:29 themes/basic/globaltoc.html:2
#: themes/basic/localtoc.html:4 themes/scrolls/layout.html:32
msgid "Table of Contents"
msgstr "Taula de continguts"

#: themes/agogo/layout.html:34 themes/basic/layout.html:130
#: themes/basic/search.html:3 themes/basic/search.html:15
msgid "Search"
msgstr "Cerca"

#: themes/agogo/layout.html:37 themes/basic/searchbox.html:8
#: themes/basic/searchfield.html:12
msgid "Go"
msgstr "Ves a"

#: themes/agogo/layout.html:81 themes/basic/sourcelink.html:7
msgid "Show Source"
msgstr "Mostra el codi font"

#: themes/haiku/layout.html:16
msgid "Contents"
msgstr "Contingut"

#: themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "Cercar a %(docstitle)s"

#: themes/basic/defindex.html:4
msgid "Overview"
msgstr "Vista general"

#: themes/basic/defindex.html:8
msgid "Welcome! This is"
msgstr "Us donem la benvinguda! Això és"

#: themes/basic/defindex.html:9
msgid "the documentation for"
msgstr "la documentació per a"

#: themes/basic/defindex.html:10
msgid "last updated"
msgstr "darrera actualització"

#: themes/basic/defindex.html:13
msgid "Indices and tables:"
msgstr "Índexs i taules:"

#: themes/basic/defindex.html:16
msgid "Complete Table of Contents"
msgstr "Taula de contingut completa"

#: themes/basic/defindex.html:17
msgid "lists all sections and subsections"
msgstr "llista totes les seccions i subseccions"

#: domains/std/__init__.py:773 domains/std/__init__.py:786
#: themes/basic/defindex.html:18
msgid "Search Page"
msgstr "Pàgina de cerca"

#: themes/basic/defindex.html:19
msgid "search this documentation"
msgstr "cerca aquesta documentació"

#: themes/basic/defindex.html:21
msgid "Global Module Index"
msgstr "Índex global de mòduls"

#: themes/basic/defindex.html:22
msgid "quick access to all modules"
msgstr "accés ràpid a tots els mòduls"

#: builders/html/__init__.py:507 themes/basic/defindex.html:23
msgid "General Index"
msgstr "Índex general"

#: themes/basic/defindex.html:24
msgid "all functions, classes, terms"
msgstr "totes les funcions, classes, termes"

#: themes/basic/sourcelink.html:4
msgid "This Page"
msgstr "Aquesta pàgina"

#: themes/basic/genindex-single.html:26
#, python-format
msgid "Index &#x2013; %(key)s"
msgstr "Índex &#x2013; %(key)s"

#: themes/basic/genindex-single.html:54 themes/basic/genindex-split.html:16
#: themes/basic/genindex-split.html:30 themes/basic/genindex.html:65
msgid "Full index on one page"
msgstr "Índex complet en una pàgina"

#: themes/basic/searchbox.html:4
msgid "Quick search"
msgstr "Cerca ràpida"

#: themes/basic/genindex-split.html:8
msgid "Index pages by letter"
msgstr "Pàgines d'índex per lletra"

#: themes/basic/genindex-split.html:17
msgid "can be huge"
msgstr "pot ser gegant"

#: themes/basic/relations.html:4
msgid "Previous topic"
msgstr "Tema anterior"

#: themes/basic/relations.html:6
msgid "previous chapter"
msgstr "capítol anterior"

#: themes/basic/relations.html:11
msgid "Next topic"
msgstr "Tema següent"

#: themes/basic/relations.html:13
msgid "next chapter"
msgstr "capítol següent"

#: themes/basic/layout.html:18
msgid "Navigation"
msgstr "Navegació"

#: themes/basic/layout.html:115
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Cerca dins de %(docstitle)s"

#: themes/basic/layout.html:124
msgid "About these documents"
msgstr "Quant a aquests documents"

#: themes/basic/layout.html:133 themes/basic/layout.html:177
#: themes/basic/layout.html:179
msgid "Copyright"
msgstr "Copyright"

#: themes/basic/layout.html:183 themes/basic/layout.html:189
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr "&#169; %(copyright_prefix)s %(copyright)s."

#: themes/basic/layout.html:201
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Darrera actualització el %(last_updated)s."

#: themes/basic/layout.html:204
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr "Creada mitjançant <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s."

#: themes/basic/search.html:20
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "Activa JavaScript per a usar la funcionalitat\n    de cerca."

#: themes/basic/search.html:28
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr "Cercar múltiples paraules només mostrarà les coincidències\n    que continguin totes les paraules."

#: themes/basic/search.html:35
msgid "search"
msgstr "cerca"

#: themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "Oculta els resultats de cerca"

#: themes/basic/static/searchtools.js:117
msgid "Search Results"
msgstr "Resultats de la cerca"

#: themes/basic/static/searchtools.js:119
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "La vostra cerca no ha coincidit amb cap document. Assegureu-vos que s'escriuen correctament totes les paraules i que heu seleccionat prou categories."

#: themes/basic/static/searchtools.js:123
#, python-brace-format
msgid "Search finished, found one page matching the search query."
msgid_plural ""
"Search finished, found ${resultCount} pages matching the search query."
msgstr[0] "La cerca ha acabat, s'ha trobat una pàgina que coincideix amb la consulta de cerca."
msgstr[1] "La cerca ha acabat, s'han trobat ${resultCount} pàgines que coincideixen amb la consulta de cerca."

#: themes/basic/static/searchtools.js:253
msgid "Searching"
msgstr "S'està cercant"

#: themes/basic/static/searchtools.js:270
msgid "Preparing search..."
msgstr "S'està preparant la cerca..."

#: themes/basic/static/searchtools.js:474
msgid ", in "
msgstr ", a "

#: themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: themes/basic/changes/frameset.html:5
#: themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "Canvis en la versió %(version)s &#8212; %(docstitle)s"

#: themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "Llista de canvis de la versió %(version)s generada automàticament"

#: themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "Canvis a la biblioteca"

#: themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "Canvis a l'API de C"

#: themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "Altres canvis"

#: themes/classic/static/sidebar.js.jinja:42
msgid "Expand sidebar"
msgstr "Expandeix la barra lateral"

#: domains/python/_annotations.py:529
msgid "Positional-only parameter separator (PEP 570)"
msgstr ""

#: domains/python/_annotations.py:540
msgid "Keyword-only parameters separator (PEP 3102)"
msgstr ""

#: domains/python/__init__.py:113 domains/python/__init__.py:278
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (al mòdul %s)"

#: domains/python/__init__.py:180 domains/python/__init__.py:374
#: domains/python/__init__.py:434 domains/python/__init__.py:474
#, python-format
msgid "%s (in module %s)"
msgstr "%s (al mòdul %s)"

#: domains/python/__init__.py:182
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (variable interna)"

#: domains/python/__init__.py:217
#, python-format
msgid "%s (built-in class)"
msgstr "%s (classe interna)"

#: domains/python/__init__.py:218
#, python-format
msgid "%s (class in %s)"
msgstr "%s (classe a %s)"

#: domains/python/__init__.py:283
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (mètode de classe %s)"

#: domains/python/__init__.py:285
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (mètode estàtic %s)"

#: domains/python/__init__.py:438
#, python-format
msgid "%s (%s property)"
msgstr "%s (propietat %s)"

#: domains/python/__init__.py:478
#, python-format
msgid "%s (type alias in %s)"
msgstr "%s (escriviu l'àlies a %s)"

#: domains/python/__init__.py:638
msgid "Python Module Index"
msgstr "Índex de mòduls en Python"

#: domains/python/__init__.py:639
msgid "modules"
msgstr "mòduls"

#: domains/python/__init__.py:717
msgid "Deprecated"
msgstr "Obsolet"

#: domains/python/__init__.py:743
msgid "exception"
msgstr "excepció"

#: domains/python/__init__.py:745
msgid "class method"
msgstr "mètode de classe"

#: domains/python/__init__.py:746
msgid "static method"
msgstr "mètode estàtic"

#: domains/python/__init__.py:748
msgid "property"
msgstr "propietat"

#: domains/python/__init__.py:749
msgid "type alias"
msgstr "escriviu l'àlies"

#: domains/python/__init__.py:818
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr "descripció de l'objecte duplicat de %s, una altra instància a %s, ús «:no-index:» per a un d'ells"

#: domains/python/__init__.py:978
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "s'ha trobat més d'un objectiu per a la referència creuada %r: %s"

#: domains/python/__init__.py:1052
msgid " (deprecated)"
msgstr " (obsolet)"

#: domains/c/__init__.py:326 domains/cpp/__init__.py:483
#: domains/python/_object.py:190 ext/napoleon/docstring.py:974
msgid "Parameters"
msgstr "Paràmetres"

#: domains/python/_object.py:206
msgid "Variables"
msgstr "Variables"

#: domains/python/_object.py:214
msgid "Raises"
msgstr "Llença"

#: domains/cpp/__init__.py:159
msgid "Template Parameters"
msgstr "Paràmetres de la plantilla"

#: domains/cpp/__init__.py:302
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: domains/cpp/__init__.py:392 domains/cpp/_symbol.py:942
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr "Declaració de C** duplicada, també definida a %s:%s.\nLa declaració és «.. cpp:%s:: %s»."

#: domains/c/__init__.py:333 domains/cpp/__init__.py:496
msgid "Return values"
msgstr "Valors retornats"

#: domains/c/__init__.py:754 domains/cpp/__init__.py:940
msgid "union"
msgstr "unió"

#: domains/c/__init__.py:749 domains/cpp/__init__.py:942
msgid "member"
msgstr "membre"

#: domains/c/__init__.py:757 domains/cpp/__init__.py:943
msgid "type"
msgstr "tipus"

#: domains/cpp/__init__.py:944
msgid "concept"
msgstr "concepte"

#: domains/c/__init__.py:755 domains/cpp/__init__.py:945
msgid "enum"
msgstr "enumera"

#: domains/c/__init__.py:756 domains/cpp/__init__.py:946
msgid "enumerator"
msgstr "numerador"

#: domains/c/__init__.py:760 domains/cpp/__init__.py:949
msgid "function parameter"
msgstr "paràmetre de la funció"

#: domains/cpp/__init__.py:952
msgid "template parameter"
msgstr "paràmetre de la plantilla"

#: domains/c/__init__.py:211
#, python-format
msgid "%s (C %s)"
msgstr "%s (C %s)"

#: domains/c/__init__.py:277 domains/c/_symbol.py:557
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr "Declaració de C duplicada, també definida a %s:%s.\nLa declaració és «.. c:%s:: %s»."

#: domains/c/__init__.py:750
msgid "variable"
msgstr "variable"

#: domains/c/__init__.py:752
msgid "macro"
msgstr "macro"

#: domains/c/__init__.py:753
msgid "struct"
msgstr "estructura"

#: domains/std/__init__.py:91 domains/std/__init__.py:111
#, python-format
msgid "environment variable; %s"
msgstr "variable d'entorn; %s"

#: domains/std/__init__.py:119
#, python-format
msgid "%s; configuration value"
msgstr "%s; valor de configuració"

#: domains/std/__init__.py:175
msgid "Type"
msgstr "Tipus"

#: domains/std/__init__.py:185
msgid "Default"
msgstr "Predeterminat"

#: domains/std/__init__.py:242
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "Descripció de l'opció amb format incorrecte %r, s'ha de veure com «opt», «-opt args», «--opt args», «/opt args» o «+opt args»"

#: domains/std/__init__.py:319
#, python-format
msgid "%s command line option"
msgstr "opció de la línia d'ordres %s"

#: domains/std/__init__.py:321
msgid "command line option"
msgstr "opció de la línia d'ordres"

#: domains/std/__init__.py:461
msgid "glossary term must be preceded by empty line"
msgstr "el terme del glossari ha d'estar precedit per una línia buida"

#: domains/std/__init__.py:474
msgid "glossary terms must not be separated by empty lines"
msgstr "els termes del glossari no han d'estar separats per línies buides"

#: domains/std/__init__.py:486 domains/std/__init__.py:504
msgid "glossary seems to be misformatted, check indentation"
msgstr "el glossari sembla estar mal formatat, verifiqueu el sagnat"

#: domains/std/__init__.py:729
msgid "glossary term"
msgstr "terme del glossari"

#: domains/std/__init__.py:730
msgid "grammar token"
msgstr "gramàtica simbòlica"

#: domains/std/__init__.py:731
msgid "reference label"
msgstr "etiqueta de referència"

#: domains/std/__init__.py:733
msgid "environment variable"
msgstr "variable d'entorn"

#: domains/std/__init__.py:734
msgid "program option"
msgstr "opció del programa"

#: domains/std/__init__.py:735
msgid "document"
msgstr "document"

#: domains/std/__init__.py:772 domains/std/__init__.py:785
msgid "Module Index"
msgstr "Índex de mòduls"

#: domains/std/__init__.py:857
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "descripció %s duplicada del %s, una altra instància a %s"

#: domains/std/__init__.py:1113
msgid "numfig is disabled. :numref: is ignored."
msgstr "«numfig» està desactivat. :numref: s'ignora."

#: domains/std/__init__.py:1124
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr "Ha fallat en crear una referència creuada. No s'assigna cap número: %s"

#: domains/std/__init__.py:1138
#, python-format
msgid "the link has no caption: %s"
msgstr "l'enllaç no té cap subtítol: %s"

#: domains/std/__init__.py:1153
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "numfig_format no vàlid: %s (%r)"

#: domains/std/__init__.py:1157
#, python-format
msgid "invalid numfig_format: %s"
msgstr "numfig_format no vàlid: %s"

#: domains/std/__init__.py:1453
#, python-format
msgid "undefined label: %r"
msgstr "etiqueta sense definir: %r"

#: domains/std/__init__.py:1456
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr "Ha fallat en crear una referència creuada. No es troba un títol o subtítol: %r"

#: environment/adapters/toctree.py:324
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "s'han detectat referències circulars del toctree, s'ignora: %s <- %s"

#: environment/adapters/toctree.py:349
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "el toctree conté una referència cap al document %r, el qual no conté un títol: no es generarà cap enllaç"

#: environment/adapters/toctree.py:364
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr "el toctree conté una referència cap a un document no inclòs %r"

#: environment/adapters/toctree.py:367
#, python-format
msgid "toctree contains reference to non-existing document %r"
msgstr ""

#: environment/adapters/indexentries.py:123
#, python-format
msgid "see %s"
msgstr "vegeu %s"

#: environment/adapters/indexentries.py:133
#, python-format
msgid "see also %s"
msgstr "vegeu també %s"

#: environment/adapters/indexentries.py:141
#, python-format
msgid "unknown index entry type %r"
msgstr "tipus d'entrada %r amb un índex desconegut"

#: environment/adapters/indexentries.py:268
#: templates/latex/sphinxmessages.sty.jinja:11
msgid "Symbols"
msgstr "Símbols"

#: environment/collectors/asset.py:98
#, python-format
msgid "image file not readable: %s"
msgstr "el fitxer d'imatge no es pot llegir: %s"

#: environment/collectors/asset.py:126
#, python-format
msgid "image file %s not readable: %s"
msgstr "el fitxer d'imatge %s no es pot llegir: %s"

#: environment/collectors/asset.py:163
#, python-format
msgid "download file not readable: %s"
msgstr "el fitxer de baixada no es pot llegir: %s"

#: environment/collectors/toctree.py:259
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s ja té assignats números de secció (toctree amb numeració imbricada?)"

#: _cli/util/errors.py:190
msgid "Interrupted!"
msgstr "S'ha interromput!"

#: _cli/util/errors.py:194
msgid "reStructuredText markup error!"
msgstr ""

#: _cli/util/errors.py:200
msgid "Encoding error!"
msgstr ""

#: _cli/util/errors.py:203
msgid "Recursion error!"
msgstr ""

#: _cli/util/errors.py:207
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1,000 in conf.py "
"with e.g.:"
msgstr ""

#: _cli/util/errors.py:227
msgid "Starting debugger:"
msgstr ""

#: _cli/util/errors.py:235
msgid "The full traceback has been saved in:"
msgstr "S'ha desat la traça completa a:"

#: _cli/util/errors.py:240
msgid ""
"To report this error to the developers, please open an issue at "
"<https://github.com/sphinx-doc/sphinx/issues/>. Thanks!"
msgstr "Per a informar d'aquest error als desenvolupadors, obriu un problema a <https://github.com/sphinx-doc/sphinx/issues/>. Gràcies!"

#: _cli/util/errors.py:246
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "Informeu-ho també si es tractava d'un error d'usuari, de manera que la pròxima vegada es pugui proporcionar un missatge d'error millor."

#: transforms/post_transforms/__init__.py:88
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr "No s'ha pogut determinar el text alternatiu per a la referència creuada. Podria ser un error."

#: transforms/post_transforms/__init__.py:237
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "s'ha trobat més d'una destinació per a «any» en la referència creuada %r: podria ser %s"

#: transforms/post_transforms/__init__.py:299
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr "%s:%s no es troba la destinació de la referència: %s"

#: transforms/post_transforms/__init__.py:305
#, python-format
msgid "%r reference target not found: %s"
msgstr "%r no es troba la destinació de la referència: %s"

#: transforms/post_transforms/images.py:79
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "No s'ha pogut recuperar la imatge remota: %s [%s]"

#: transforms/post_transforms/images.py:96
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "No s'ha pogut recuperar la imatge remota: %s [%d]"

#: transforms/post_transforms/images.py:143
#, python-format
msgid "Unknown image format: %s..."
msgstr "Format d'imatge desconegut: %s..."

#: builders/html/__init__.py:113
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "Les pàgines en HTML es troben a %(outdir)s."

#: builders/html/__init__.py:348
#, python-format
msgid "Failed to read build info file: %r"
msgstr "Ha fallat en llegir el fitxer d'informació de la construcció: %r"

#: builders/html/__init__.py:364
msgid "build_info mismatch, copying .buildinfo to .buildinfo.bak"
msgstr "build_info no coincideix, s'està copiant .buildinfo a .buildinfo.bak"

#: builders/html/__init__.py:366
msgid "building [html]: "
msgstr "es construeix [html]:"

#: builders/html/__init__.py:383
#, python-format
msgid ""
"template %s has been changed since the previous build, all docs will be "
"rebuilt"
msgstr "s'ha canviat la plantilla %s des de la compilació anterior, es reconstruiran tots els documents"

#: builders/html/__init__.py:507
msgid "index"
msgstr "índex"

#: builders/html/__init__.py:560
#, python-format
msgid "Logo of %s"
msgstr "Logotip de %s"

#: builders/html/__init__.py:589
msgid "next"
msgstr "següent"

#: builders/html/__init__.py:598
msgid "previous"
msgstr "anterior"

#: builders/html/__init__.py:696
msgid "generating indices"
msgstr "s'estan generant els índexs"

#: builders/html/__init__.py:711
msgid "writing additional pages"
msgstr "s'estan escrivint les pàgines addicionals"

#: builders/html/__init__.py:794
#, python-format
msgid "cannot copy image file '%s': %s"
msgstr "no es pot copiar el fitxer d'imatge «%s»: %s"

#: builders/html/__init__.py:806
msgid "copying downloadable files... "
msgstr "s'estan copiant els fitxers que es poden baixar... "

#: builders/html/__init__.py:818
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "no s'ha pogut copiar el fitxer que es podia baixar %r: %s"

#: builders/html/__init__.py:864
#, python-format
msgid "Failed to copy a file in the theme's 'static' directory: %s: %r"
msgstr "Ha fallat en copiar un fitxer al directori «static» del tema: %s: %r"

#: builders/html/__init__.py:882
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr "Ha fallat en copiar un fitxer a html_static_file: %s: %r"

#: builders/html/__init__.py:917
msgid "copying static files"
msgstr "s'estan copiant els fitxers estàtics"

#: builders/html/__init__.py:934
#, python-format
msgid "cannot copy static file %r"
msgstr "no s'ha pogut copiar el fitxer estàtic %r"

#: builders/html/__init__.py:939
msgid "copying extra files"
msgstr "s'estan copiant els fitxers addicionals"

#: builders/html/__init__.py:949
#, python-format
msgid "cannot copy extra file %r"
msgstr "no s'ha pogut copiar el fitxer addicional %r"

#: builders/html/__init__.py:955
#, python-format
msgid "Failed to write build info file: %r"
msgstr "Ha fallat en escriure el fitxer d'informació de la construcció: %r"

#: builders/html/__init__.py:1005
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "no s'ha pogut carregar l'índex de cerca, i no es construiran tots els documents: l'índex estarà incomplet."

#: builders/html/__init__.py:1052
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "la pàgina %s coincideix amb dos patrons a html_sidebars: %r i %r"

#: builders/html/__init__.py:1216
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "s'ha produït un error d'Unicode en representar la pàgina %s. Assegureu-vos que tots els valors de configuració que contenen contingut que no és ASCII són cadenes Unicode."

#: builders/html/__init__.py:1224
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "S'ha produït un error en representar la pàgina %s.\nMotiu: %r"

#: builders/html/__init__.py:1257
msgid "dumping object inventory"
msgstr "s'està bolcant l'inventari d'objectes"

#: builders/html/__init__.py:1265
#, python-format
msgid "dumping search index in %s"
msgstr "s'està bolcant l'índex de cerca a %s"

#: builders/html/__init__.py:1308
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "js_file no vàlid: %r, s'ignora"

#: builders/html/__init__.py:1342
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "S'han enregistrat molts math_renderer. Però no s'ha seleccionat math_renderer."

#: builders/html/__init__.py:1346
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "S'ha donat un math_renderer %r desconegut."

#: builders/html/__init__.py:1360
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "l'entrada html_extra_path %r es col·loca dins del directori de sortida"

#: builders/html/__init__.py:1365
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "l'entrada html_extra_path %r no existeix"

#: builders/html/__init__.py:1380
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "l'entrada html_static_path %r es col·loca dins del directori de sortida"

#: builders/html/__init__.py:1385
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "l'entrada html_static_path %r no existeix"

#: builders/html/__init__.py:1396 builders/latex/__init__.py:504
#, python-format
msgid "logo file %r does not exist"
msgstr "el fitxer de logotip %r no existeix"

#: builders/html/__init__.py:1407
#, python-format
msgid "favicon file %r does not exist"
msgstr "el fitxer icona de web %r no existeix"

#: builders/html/__init__.py:1420
#, python-format
msgid ""
"Values in 'html_sidebars' must be a list of strings. At least one pattern "
"has a string value: %s. Change to `html_sidebars = %r`."
msgstr "Els valors a «html_sidebars» han de ser una llista de cadenes. Almenys un patró té un valor de cadena: %s. Canvia a «html_sidebars = %r»."

#: builders/html/__init__.py:1433
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr "HTML 4 ja no és compatible amb Sphinx. (s'ha detectat «html4_writer=true» a les opcions de configuració)"

#: builders/html/__init__.py:1449
#, python-format
msgid "%s %s documentation"
msgstr "%s %s documentació"

#: builders/html/_build_info.py:32
msgid "failed to read broken build info file (unknown version)"
msgstr "s'ha fallat en llegir el fitxer d'informació de la compilació trencada (versió desconeguda)"

#: builders/html/_build_info.py:36
msgid "failed to read broken build info file (missing config entry)"
msgstr "s'ha fallat en llegir el fitxer d'informació de la compilació trencada (manca l'entrada «config»)"

#: builders/html/_build_info.py:39
msgid "failed to read broken build info file (missing tags entry)"
msgstr "s'ha fallat en llegir el fitxer d'informació de la compilació trencada (manca l'entrada «tags»)"

#: builders/latex/__init__.py:118
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "Els fitxers en LaTeX es troben a %(outdir)s."

#: builders/latex/__init__.py:121
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\nExecuteu l'ordre «make» en aquest directori per a executar-les\nmitjançant el (pdf)latex (useu l'ordre «make latexpdf» per a fer-ho\nautomàticament)."

#: builders/latex/__init__.py:159
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "no s'ha trobat el valor de configuració «latex_documents»: no s'escriurà cap document"

#: builders/latex/__init__.py:170
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "El valor de configuració «latex_documents» fa referència a un document %s desconegut"

#: builders/latex/__init__.py:209 templates/latex/latex.tex.jinja:91
msgid "Release"
msgstr "Versió"

#: builders/latex/__init__.py:428
msgid "copying TeX support files"
msgstr "s'estan copiant els fitxers de suport de TeX"

#: builders/latex/__init__.py:465
msgid "copying additional files"
msgstr "s'estan copiant els fitxers addicionals"

#: builders/latex/__init__.py:536
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "Clau de configuració desconeguda: latex_elements[%r], s'ignora."

#: builders/latex/__init__.py:544
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr "Opció desconeguda de tema: latex_theme_options[%r], s'ignora."

#: builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr "Ha fallat en obtenir el docname!"

#: builders/latex/transforms.py:121
#, python-format
msgid "Failed to get a docname for source %r!"
msgstr "S'ha fallat en obtenir un nom de document per a la font %r!"

#: builders/latex/transforms.py:487
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr "No s'ha trobat cap nota a peu de pàgina per al node de referència %r donat"

#: builders/latex/theming.py:88
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r no té la configuració «theme»"

#: builders/latex/theming.py:91
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r no té la configuració «%s»"

#: templates/latex/longtable.tex.jinja:52
#: templates/latex/sphinxmessages.sty.jinja:8
msgid "continued from previous page"
msgstr "ve de la pàgina anterior"

#: templates/latex/longtable.tex.jinja:63
#: templates/latex/sphinxmessages.sty.jinja:9
msgid "continues on next page"
msgstr "continua a la pàgina següent"

#: templates/latex/sphinxmessages.sty.jinja:10
msgid "Non-alphabetical"
msgstr "No alfabètic"

#: templates/latex/sphinxmessages.sty.jinja:12
msgid "Numbers"
msgstr "Números"

#: templates/latex/sphinxmessages.sty.jinja:13
msgid "page"
msgstr "pàgina"

#: ext/napoleon/__init__.py:356 ext/napoleon/docstring.py:940
msgid "Keyword Arguments"
msgstr "Arguments de paraules clau"

#: ext/napoleon/docstring.py:176
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr "conjunt de valors no vàlid (manca el claudàtor de tancament): %s"

#: ext/napoleon/docstring.py:183
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr "conjunt de valors no vàlid (manca el claudàtor d'obertura): %s"

#: ext/napoleon/docstring.py:190
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr "Cadena incorrecta literal (manquen les cometes de tancament): %s"

#: ext/napoleon/docstring.py:197
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr "Cadena incorrecta literal (manquen les cometes d'obertura): %s"

#: ext/napoleon/docstring.py:895
msgid "Example"
msgstr "Exemple"

#: ext/napoleon/docstring.py:896
msgid "Examples"
msgstr "Exemples"

#: ext/napoleon/docstring.py:956
msgid "Notes"
msgstr "Notes"

#: ext/napoleon/docstring.py:965
msgid "Other Parameters"
msgstr "Altres paràmetres"

#: ext/napoleon/docstring.py:1001
msgid "Receives"
msgstr "Rebudes"

#: ext/napoleon/docstring.py:1005
msgid "References"
msgstr "Referències"

#: ext/napoleon/docstring.py:1037
msgid "Warns"
msgstr "Avisos"

#: ext/napoleon/docstring.py:1041
msgid "Yields"
msgstr "Rendiments"

#: ext/autosummary/__init__.py:284
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr "referències autosummary excloses del document %r. S'ignora."

#: ext/autosummary/__init__.py:288
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr "autosummary: no s'ha trobat el fitxer stub %r. Verifiqueu la vostra configuració autosummary_generate."

#: ext/autosummary/__init__.py:309
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr "Un resum automàtic amb subtítols requereix l'opció «:toctree:». S'ignora."

#: ext/autosummary/__init__.py:384
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "autosummary: ha fallat en importar %s.\nPossibles pistes:\n%s"

#: ext/autosummary/__init__.py:404
#, python-format
msgid "failed to parse name %s"
msgstr "ha fallat en analitzar el nom %s"

#: ext/autosummary/__init__.py:412
#, python-format
msgid "failed to import object %s"
msgstr "ha fallat en importar l'objecte %s"

#: ext/autosummary/__init__.py:730
#, python-format
msgid ""
"Summarised items should not include the current module. Replace %r with %r."
msgstr "Els elements resumits no han d'incloure el mòdul actual. Substituïu %r per %r."

#: ext/autosummary/__init__.py:927
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr "autosummary_generate: no s'ha trobat el fitxer: %s"

#: ext/autosummary/__init__.py:937
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr "«autosummary» genera internament els fitxers «.rst». Però el vostre source_suffix no conté cap «.rst». S'omet."

#: ext/autosummary/generate.py:232 ext/autosummary/generate.py:450
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr "autosummary: ha fallat en determinar %r que s'ha de documentar, s'ha plantejat l'excepció següent:\n%s"

#: ext/autosummary/generate.py:588
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[autosummary] s'està generant autosummary per a: %s"

#: ext/autosummary/generate.py:592
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[autosummary] s'està escrivint a %s"

#: ext/autosummary/generate.py:637
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "[autosummary]: ha fallat en importar %s.\nPossibles pistes:\n%s"

#: ext/autosummary/generate.py:836
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\nGenera ReStructuredText mitjançant directrius de resum automàtic «autosummary».\n\n«sphinx-autogen» és un frontal per a sphinx.ext.autosummary.generate.\nGenera els fitxers en reStructuredText des de les directrius d'autosummary\ncontingudes en els fitxers d'entrada indicats.\n\nEl format de les directrius d'autosummary està documentat en el mòdul\n``sphinx.ext.autosummary`` de Python i es pot llegir mitjançant l'ordre següent::\n\npydoc sphinx.ext.autosummary\n"

#: ext/autosummary/generate.py:858
msgid "source files to generate rST files for"
msgstr "fitxers font per a generar els fitxers rST per a"

#: ext/autosummary/generate.py:866
msgid "directory to place all output in"
msgstr "directori per a col·locar tota la sortida a"

#: ext/autosummary/generate.py:874
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "sufix predeterminat per als fitxers (predeterminat: %(default)s)"

#: ext/autosummary/generate.py:882
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "directori de plantilles personalitzades (predeterminat: %(default)s)"

#: ext/autosummary/generate.py:890
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "document de membres importats (predeterminat: %(default)s)"

#: ext/autosummary/generate.py:899
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr "documenta exactament els membres en l'atribut __all__ del mòdul. (predeterminat: %(default)s)"

#: ext/apidoc/_cli.py:178 ext/autosummary/generate.py:909
msgid "Remove existing files in the output directory that were not generated"
msgstr "Elimina els fitxers existents al directori de sortida que no s'hagin generat"

#: ext/apidoc/_shared.py:29 ext/autosummary/generate.py:944
#, python-format
msgid "Failed to remove %s: %s"
msgstr "S'ha fallat en eliminar %s: %s"

#: ext/apidoc/_cli.py:28
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\nMireu recursivament a <MODULE_PATH> per als mòduls i paquets de Python\ni creeu un fitxer reST amb les directives «automodule» per paquet en el <OUTPUT_PATH>.\n\nEls <EXCLUDE_PATTERN> poden ser fitxers i/o patrons de directori que seran\nexclosos de la generació.\n\nNota: De manera predeterminada, aquest script no sobreescriurà els fitxers que ja s'han creat."

#: ext/apidoc/_cli.py:45
msgid "path to module to document"
msgstr "camí cap al mòdul que es documenta"

#: ext/apidoc/_cli.py:50
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "fitxer d'estil fnmatch i/o patrons de directori que s'exclouran de la generació"

#: ext/apidoc/_cli.py:60
msgid "directory to place all output"
msgstr "directori per a col·locar tota la sortida"

#: ext/apidoc/_cli.py:75
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "profunditat màxima dels submòduls que es mostraran a la TOC (predeterminada: 4)"

#: ext/apidoc/_cli.py:82
msgid "overwrite existing files"
msgstr "sobreescriu els fitxers existents"

#: ext/apidoc/_cli.py:91
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "seguir els enllaços simbòlics. Potent quan es combina amb el paquet collective.recipe.omelette."

#: ext/apidoc/_cli.py:99
msgid "run the script without creating files"
msgstr "executa l'script sense crear els fitxers"

#: ext/apidoc/_cli.py:106
msgid "put documentation for each module on its own page"
msgstr "posa la documentació per a cada mòdul a la seva pròpia pàgina"

#: ext/apidoc/_cli.py:113
msgid "include \"_private\" modules"
msgstr "inclou «_private» en els mòduls"

#: ext/apidoc/_cli.py:120
msgid "filename of table of contents (default: modules)"
msgstr "nom de fitxer de la taula de contingut (predeterminat: mòduls)"

#: ext/apidoc/_cli.py:127
msgid "don't create a table of contents file"
msgstr "no crea un fitxer de taula de contingut"

#: ext/apidoc/_cli.py:135
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "no crea capçaleres per als paquets del mòdul/paquet (p. ex., quan les cadenes de documentació ja les contenen)"

#: ext/apidoc/_cli.py:145
msgid "put module documentation before submodule documentation"
msgstr "posa la documentació del mòdul abans de la documentació del submòdul"

#: ext/apidoc/_cli.py:152
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "interpreta els camins dels mòduls segons l'especificació d'espais de noms implícits al PEP-0420"

#: ext/apidoc/_cli.py:160
msgid ""
"Comma-separated list of options to pass to automodule directive (or use "
"SPHINX_APIDOC_OPTIONS)."
msgstr ""

#: ext/apidoc/_cli.py:170
msgid "file suffix (default: rst)"
msgstr "sufix del fitxer (predeterminat: rst)"

#: ext/apidoc/_cli.py:186
msgid "generate a full project with sphinx-quickstart"
msgstr "genera un projecte complet amb «sphinx-quickstart»"

#: ext/apidoc/_cli.py:193
msgid "append module_path to sys.path, used when --full is given"
msgstr "afegeix module_path a sys.path, s'usa quan s'indica el paràmetre «--full»"

#: ext/apidoc/_cli.py:200
msgid "project name (default: root module name)"
msgstr "nom del projecte (predeterminat: nom del mòdul arrel)"

#: ext/apidoc/_cli.py:207
msgid "project author(s), used when --full is given"
msgstr "autoria del projecte, s'usa quan s'indica el paràmetre «--full»"

#: ext/apidoc/_cli.py:214
msgid "project version, used when --full is given"
msgstr "versió del projecte, s'usa quan s'indica el paràmetre «--full»"

#: ext/apidoc/_cli.py:222
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "llançament del projecte, s'usa quan s'indica el paràmetre «--full», predeterminat a «--doc-version»"

#: ext/apidoc/_cli.py:226
msgid "extension options"
msgstr "opcions de l'extensió"

#: ext/apidoc/_cli.py:232
msgid "enable arbitrary extensions, used when --full is given"
msgstr ""

#: ext/apidoc/_cli.py:240
#, python-format
msgid "enable %s extension, used when --full is given"
msgstr ""

#: ext/apidoc/_cli.py:291
#, python-format
msgid "%s is not a directory."
msgstr "%s no és cap directori."

#: ext/apidoc/_extension.py:50
msgid "Running apidoc"
msgstr ""

#: ext/apidoc/_extension.py:102
#, python-format
msgid "apidoc_modules item %i must be a dict"
msgstr ""

#: ext/apidoc/_extension.py:110
#, python-format
msgid "apidoc_modules item %i must have a 'path' key"
msgstr ""

#: ext/apidoc/_extension.py:115
#, python-format
msgid "apidoc_modules item %i 'path' must be a string"
msgstr ""

#: ext/apidoc/_extension.py:121
#, python-format
msgid "apidoc_modules item %i 'path' is not an existing folder: %s"
msgstr ""

#: ext/apidoc/_extension.py:133
#, python-format
msgid "apidoc_modules item %i must have a 'destination' key"
msgstr ""

#: ext/apidoc/_extension.py:140
#, python-format
msgid "apidoc_modules item %i 'destination' must be a string"
msgstr ""

#: ext/apidoc/_extension.py:147
#, python-format
msgid "apidoc_modules item %i 'destination' should be a relative path"
msgstr ""

#: ext/apidoc/_extension.py:157
#, python-format
msgid "apidoc_modules item %i cannot create destination directory: %s"
msgstr ""

#: ext/apidoc/_extension.py:178
#, python-format
msgid "apidoc_modules item %i '%s' must be an int"
msgstr ""

#: ext/apidoc/_extension.py:192
#, python-format
msgid "apidoc_modules item %i '%s' must be a boolean"
msgstr ""

#: ext/apidoc/_extension.py:210
#, python-format
msgid "apidoc_modules item %i has unexpected keys: %s"
msgstr ""

#: ext/apidoc/_extension.py:247
#, python-format
msgid "apidoc_modules item %i '%s' must be a sequence"
msgstr ""

#: ext/apidoc/_extension.py:256
#, python-format
msgid "apidoc_modules item %i '%s' must contain strings"
msgstr ""

#: ext/apidoc/_generate.py:69
#, python-format
msgid "Would create file %s."
msgstr "S'hauria de crear el fitxer %s."

#: ext/intersphinx/_resolve.py:49
#, python-format
msgid "(in %s v%s)"
msgstr "(a %s versió %s)"

#: ext/intersphinx/_resolve.py:51
#, python-format
msgid "(in %s)"
msgstr "(a %s)"

#: ext/intersphinx/_resolve.py:108
#, python-format
msgid "inventory '%s': duplicate matches found for %s:%s"
msgstr "inventari «%s»: s'han trobat coincidències duplicades per a %s:%s"

#: ext/intersphinx/_resolve.py:118
#, python-format
msgid "inventory '%s': multiple matches found for %s:%s"
msgstr "inventari «%s»: s'han trobat múltiples coincidències per a %s:%s"

#: ext/intersphinx/_resolve.py:383
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr "no es troba l'inventari per a la referència creuada externa: %r"

#: ext/intersphinx/_resolve.py:392
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr "no es troba l'inventari per a la referència creuada externa: %r"

#: ext/intersphinx/_resolve.py:403
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr "no es troba el domini per a la referència creuada externa: %r"

#: ext/intersphinx/_resolve.py:619
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr "%s externa: no es troba la destinació de la referència %s: %s"

#: ext/intersphinx/_load.py:60
#, python-format
msgid ""
"Invalid intersphinx project identifier `%r` in intersphinx_mapping. Project "
"identifiers must be non-empty strings."
msgstr "L'identificador «%r» del projecte intersphinx no és vàlid a intersphinx_mapping. Els identificadors de projecte han de ser cadenes no buides."

#: ext/intersphinx/_load.py:71
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Expected a two-element tuple "
"or list."
msgstr "El valor «%r» no és vàlid a intersphinx_mapping[%r]. S'esperava una tupla o llista de dos elements."

#: ext/intersphinx/_load.py:82
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Values must be a (target URI,"
" inventory locations) pair."
msgstr "El valor «%r» no és vàlid a intersphinx_mapping[%r]. Els valors han de ser un parell (URI de destinació, ubicacions de l'inventari)."

#: ext/intersphinx/_load.py:93
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique non-empty strings."
msgstr "El valor «%r» de l'URI de destinació no és vàlid a intersphinx_mapping[%r][0]. Els URI de destinació han de ser cadenes úniques no buides."

#: ext/intersphinx/_load.py:102
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique (other instance in intersphinx_mapping[%r])."
msgstr "El valor «%r» de l'URI de destinació no és vàlid a intersphinx_mapping[%r][0]. Els URI de destinació han de ser únics (una altra instància a intersphinx_mapping[%r])."

#: ext/intersphinx/_load.py:121
#, python-format
msgid ""
"Invalid inventory location value `%r` in intersphinx_mapping[%r][1]. "
"Inventory locations must be non-empty strings or None."
msgstr "El valor «%r» d'ubicació de l'inventari no és vàlid a intersphinx_mapping[%r][1]. Les ubicacions de l'inventari han de ser cadenes no buides o «None»."

#: ext/intersphinx/_load.py:131
msgid "Invalid `intersphinx_mapping` configuration (1 error)."
msgstr "Configuració d'«intersphinx_mapping» no vàlida (1 error)."

#: ext/intersphinx/_load.py:134
#, python-format
msgid "Invalid `intersphinx_mapping` configuration (%s errors)."
msgstr "Configuració d'«intersphinx_mapping» no vàlida (%s errors)."

#: ext/intersphinx/_load.py:157
msgid "An invalid intersphinx_mapping entry was added after normalisation."
msgstr "S'ha afegit una entrada intersphinx_mapping no vàlida després de la normalització."

#: ext/intersphinx/_load.py:261
#, python-format
msgid "loading intersphinx inventory '%s' from %s ..."
msgstr "s'està carregant l'inventari intersphinx «%s» de %s ..."

#: ext/intersphinx/_load.py:287
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "s'han trobat alguns problemes amb alguns dels inventaris, però tenien alternatives funcionals:"

#: ext/intersphinx/_load.py:297
msgid "failed to reach any of the inventories with the following issues:"
msgstr "s'ha fallat en arribar a cap dels inventaris amb els problemes següents:"

#: ext/intersphinx/_load.py:361
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "s'ha mogut l'inventari intersphinx: %s -> %s"

#: ext/autodoc/__init__.py:150
#, python-format
msgid "invalid value for member-order option: %s"
msgstr "valor no vàlid per a l'opció de l'ordre de membre: %s"

#: ext/autodoc/__init__.py:158
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr "valor no vàlid per a l'opció des de la documentació de classes: %s"

#: ext/autodoc/__init__.py:460
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "signatura no vàlida per a auto%s (%r)"

#: ext/autodoc/__init__.py:579
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "error mentre es donava format als arguments per a %s: %s"

#: ext/autodoc/__init__.py:898
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr "autodoc: ha fallat en determinar %s. %s (%r) que s'ha de documentar, s'ha plantejat l'excepció següent:\n%s"

#: ext/autodoc/__init__.py:1021
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "es desconeix quin és el mòdul que s'importarà per al document automàtic %r (proveu de col·locar una directiva «module» o «currentmodule» en el document o doneu-li un nom explícit al mòdul)"

#: ext/autodoc/__init__.py:1080
#, python-format
msgid "A mocked object is detected: %r"
msgstr "S'ha detectat un objecte simulat: %r"

#: ext/autodoc/__init__.py:1103
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr "error mentre es donava format a la signatura per a %s: %s"

#: ext/autodoc/__init__.py:1177
msgid "\"::\" in automodule name doesn't make sense"
msgstr "«::» en el nom de l'«automodule» no té sentit"

#: ext/autodoc/__init__.py:1185
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr "arguments de signatura o anotació de retorn indicats per a «automodule» %s"

#: ext/autodoc/__init__.py:1201
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__ ha de ser una llista de cadenes, no %r (en el mòdul %s) -s'ignora __all__-"

#: ext/autodoc/__init__.py:1278
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr "manca l'atribut esmentat a l'opció «:members:»: mòdul %s, atribut %s"

#: ext/autodoc/__init__.py:1505 ext/autodoc/__init__.py:1593
#: ext/autodoc/__init__.py:3127
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr "Ha fallat en obtenir una signatura de funció per a %s: %s"

#: ext/autodoc/__init__.py:1828
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr "Ha fallat en obtenir un constructor de funció per a %s: %s"

#: ext/autodoc/__init__.py:1966
#, python-format
msgid "Bases: %s"
msgstr "Bases: %s"

#: ext/autodoc/__init__.py:1985
#, python-format
msgid "missing attribute %s in object %s"
msgstr "manca l'atribut %s a l'objecte %s"

#: ext/autodoc/__init__.py:2081 ext/autodoc/__init__.py:2110
#: ext/autodoc/__init__.py:2204
#, python-format
msgid "alias of %s"
msgstr "àlies de %s"

#: ext/autodoc/__init__.py:2097
#, python-format
msgid "alias of TypeVar(%s)"
msgstr "àlies de TypeVar(%s)"

#: ext/autodoc/__init__.py:2456 ext/autodoc/__init__.py:2576
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr "Ha fallat en obtenir una signatura de mètode per a %s: %s"

#: ext/autodoc/__init__.py:2720
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr "S'han trobat __slots__ no vàlids a %s. S'ignora."

#: ext/autodoc/preserve_defaults.py:195
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr "Ha fallat en analitzar un valor d'argument predeterminat per a %r: %s"

#: ext/autodoc/type_comment.py:151
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr "Ha fallat en actualitzar la signatura per a %r: no es troba el paràmetre: %s"

#: ext/autodoc/type_comment.py:154
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr "Ha fallat en analitzar type_comment per a %r: %s"
