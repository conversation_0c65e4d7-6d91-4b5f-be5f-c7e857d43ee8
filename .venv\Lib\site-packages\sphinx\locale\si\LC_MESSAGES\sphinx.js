Documentation.addTranslations({
    "locale": "si",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": "",
        "About these documents": "\u0db8\u0dd9\u0db8 \u0dbd\u0dda\u0d9b\u0dab \u0d9c\u0dd0\u0db1",
        "Automatically generated list of changes in version %(version)s": "",
        "C API changes": "C API \u0dc0\u0dd9\u0db1\u0dc3\u0dca\u0d9a\u0db8\u0dca",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "",
        "Collapse sidebar": "",
        "Complete Table of Contents": "\u0dc3\u0db8\u0dca\u0db4\u0dd6\u0dbb\u0dca\u0dab \u0db4\u0da7\u0dd4\u0db1",
        "Contents": "\u0d85\u0db1\u0dca\u0dad\u0dbb\u0dca\u0d9c\u0dad\u0dba",
        "Copyright": "",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "",
        "Full index on one page": "",
        "General Index": "",
        "Global Module Index": "",
        "Go": "\u0dba\u0db1\u0dca\u0db1",
        "Hide Search Matches": "",
        "Index": "",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "",
        "Indices and tables:": "",
        "Last updated on %(last_updated)s.": "",
        "Library changes": "\u0db4\u0dd4\u0dc3\u0dca\u0dad\u0d9a\u0dcf\u0dbd \u0dc0\u0dd9\u0db1\u0dc3\u0dca\u0d9a\u0db8\u0dca",
        "Navigation": "\u0d9c\u0db8\u0db1\u0dca \u0d9a\u0dd2\u0dbb\u0dd3\u0db8",
        "Next topic": "\u0d8a\u0dc5\u0d9f \u0db8\u0dcf\u0dad\u0dd8\u0d9a\u0dcf\u0dc0",
        "Other changes": "\u0dc0\u0dd9\u0db1\u0dad\u0dca \u0dc0\u0dd9\u0db1\u0dc3\u0dca\u0d9a\u0db8\u0dca",
        "Overview": "",
        "Please activate JavaScript to enable the search\n    functionality.": "",
        "Preparing search...": "\u0dc3\u0dd9\u0dc0\u0dd4\u0db8 \u0dc3\u0dd6\u0daf\u0dcf\u0db1\u0db8\u0dca \u0d9a\u0dbb\u0db8\u0dd2\u0db1\u0dca....",
        "Previous topic": "\u0db4\u0dd9\u0dbb \u0db8\u0dcf\u0dad\u0dd8\u0d9a\u0dcf\u0dc0",
        "Quick search": "\u0d89\u0d9a\u0dca\u0db8\u0db1\u0dca \u0dc3\u0dd9\u0dc0\u0dd4\u0db8",
        "Search": "\u0dc3\u0ddc\u0dba\u0db1\u0dca\u0db1",
        "Search Page": "\u0dc3\u0dd9\u0dc0\u0dd4\u0db8\u0dca \u0db4\u0dd2\u0da7\u0dd4\u0dc0",
        "Search Results": "\u0dc3\u0dd9\u0dc0\u0dd4\u0db8\u0dca \u0db4\u0dca\u200d\u0dbb\u0dad\u0dd2\u0db5\u0dbd",
        "Search finished, found one page matching the search query.": [
            "",
            ""
        ],
        "Search within %(docstitle)s": "",
        "Searching": "\u0dc3\u0ddc\u0dba\u0db8\u0dd2\u0db1\u0dca...",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "\u0db8\u0dd6\u0dbd\u0dba \u0db4\u0dd9\u0db1\u0dca\u0dc0\u0db1\u0dca\u0db1",
        "Table of Contents": "",
        "This Page": "\u0db8\u0dd9\u0db8 \u0db4\u0dd2\u0da7\u0dd4\u0dc0",
        "Welcome! This is": "",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "",
        "all functions, classes, terms": "",
        "can be huge": "\u0dc0\u0dd2\u0dc1\u0dcf\u0dbd \u0dc0\u0dd2\u0dba \u0dc4\u0dd0\u0d9a",
        "last updated": "\u0d85\u0dc0\u0dc3\u0db1\u0dca\u0dc0\u0dbb\u0da7 \u0dba\u0dcf\u0dc0\u0dad\u0dca\u0d9a\u0dcf\u0dbd \u0d9a\u0dbd",
        "lists all sections and subsections": "",
        "next chapter": "\u0d8a\u0dc5\u0d9f \u0db4\u0dbb\u0dd2\u0da0\u0dca\u0da1\u0dda\u0daf\u0dba",
        "previous chapter": "\u0db4\u0dd9\u0dbb \u0db4\u0dbb\u0dd2\u0da0\u0dca\u0da1\u0dda\u0daf\u0dba",
        "quick access to all modules": "",
        "search": "\u0dc3\u0ddc\u0dba\u0db1\u0dca\u0db1",
        "search this documentation": "",
        "the documentation for": ""
    },
    "plural_expr": "(n != 1)"
});