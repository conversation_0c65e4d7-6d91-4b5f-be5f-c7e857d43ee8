Documentation.addTranslations({
    "locale": "hu",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": ", ",
        "About these documents": "N\u00e9vjegy ezekr\u0151l a dokumentumokr\u00f3l",
        "Automatically generated list of changes in version %(version)s": "Automatikusan gener\u00e1lt v\u00e1ltoz\u00e1slista a(z) %(version)s v\u00e1ltozathoz",
        "C API changes": "C API v\u00e1ltoz\u00e1sok",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "",
        "Collapse sidebar": "Oldals\u00e1v \u00f6sszez\u00e1r\u00e1sa",
        "Complete Table of Contents": "Teljes tartalomjegyz\u00e9k",
        "Contents": "Tartalom",
        "Copyright": "Minden jog fenntartva",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "Oldals\u00e1v kinyit\u00e1sa",
        "Full index on one page": "Teljes t\u00e1rgymutat\u00f3 egy oldalon",
        "General Index": "\u00c1ltal\u00e1nos t\u00e1rgymutat\u00f3",
        "Global Module Index": "Teljes modul t\u00e1rgymutat\u00f3",
        "Go": "Ok",
        "Hide Search Matches": "Keres\u00e9si Tal\u00e1latok Elrejt\u00e9se",
        "Index": "T\u00e1rgymutat\u00f3",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "Oldalak ABC sorrendben",
        "Indices and tables:": "T\u00e1rgymutat\u00f3 \u00e9s t\u00e1bl\u00e1zatok",
        "Last updated on %(last_updated)s.": "Utols\u00f3 friss\u00edt\u00e9s %(last_updated)s.",
        "Library changes": "K\u00f6nyvt\u00e1r v\u00e1ltoz\u00e1sok",
        "Navigation": "Navig\u00e1ci\u00f3",
        "Next topic": "K\u00f6vetkez\u0151 t\u00e9mak\u00f6r",
        "Other changes": "Egy\u00e9b v\u00e1ltoz\u00e1sok",
        "Overview": "\u00c1ttekint\u00e9s",
        "Please activate JavaScript to enable the search\n    functionality.": "K\u00e9rem enged\u00e9lyezze a JavaScriptet a keres\u0151 funkci\u00f3\n    haszn\u00e1lat\u00e1hoz.",
        "Preparing search...": "Felk\u00e9sz\u00fcl\u00e9s a keres\u00e9sre...",
        "Previous topic": "El\u0151z\u0151 t\u00e9mak\u00f6r",
        "Quick search": "Gyorskeres\u00e9s",
        "Search": "Keres\u00e9s",
        "Search Page": "Keres\u00e9s",
        "Search Results": "Keres\u00e9si Eredm\u00e9nyek",
        "Search finished, found one page matching the search query.": [
            "",
            ""
        ],
        "Search within %(docstitle)s": "Keres\u00e9s k\u00f6zt\u00fck: %(docstitle)s",
        "Searching": "Keres\u00e9s folyamatban",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "Forr\u00e1s megtekint\u00e9se",
        "Table of Contents": "",
        "This Page": "Ez az Oldal",
        "Welcome! This is": "\u00dcdv\u00f6z\u00f6lj\u00fck! Ez a",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "A keres\u00e9se nem hozott eredm\u00e9nyt. Ellen\u0151rizze, a megadott kulcsszavakat \u00e9s azt, hogy megfelel\u0151 sz\u00e1m\u00fa kateg\u00f3ria van-e kiv\u00e1lasztva.",
        "all functions, classes, terms": "\u00f6sszes funkci\u00f3, oszt\u00e1ly \u00e9s kifejez\u00e9s",
        "can be huge": "nagy lehet",
        "last updated": "utolj\u00e1ra friss\u00edtve",
        "lists all sections and subsections": "kilist\u00e1zza az \u00f6sszes fejezetet \u00e9s alfejezetet",
        "next chapter": "k\u00f6vetkez\u0151 fejezet",
        "previous chapter": "el\u0151z\u0151 fejezet",
        "quick access to all modules": "gyors hozz\u00e1f\u00e9r\u00e9s az \u00f6sszes modulhoz",
        "search": "keres\u00e9s",
        "search this documentation": "keres\u00e9s ebben a dokument\u00e1ci\u00f3ban",
        "the documentation for": "dokument\u00e1ci\u00f3"
    },
    "plural_expr": "(n != 1)"
});