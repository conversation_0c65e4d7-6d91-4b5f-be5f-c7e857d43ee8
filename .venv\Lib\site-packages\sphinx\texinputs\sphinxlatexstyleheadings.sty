%% TITLES
%
% change this info string if making any custom modification
\ProvidesPackage{sphinxlatexstyleheadings}[2023/02/11 headings]

\RequirePackage[nobottomtitles*]{titlesec}
% tests showed that this setting guarantees \section title has
% 3 lines of text following it on same page if near bottom.
\renewcommand\bottomtitlespace{6\baselineskip}
% the default setting of 0.2\textheight is about 11\baselineskip
% (for 10pt letterpaper documents) and may create large voids.

\@ifpackagelater{titlesec}{2016/03/15}%
 {\@ifpackagelater{titlesec}{2016/03/21}%
  {}%
  {\newif\ifsphinx@ttlpatch@ok
   \IfFileExists{etoolbox.sty}{%
     \RequirePackage{etoolbox}%
     \patchcmd{\ttlh@hang}{\parindent\z@}{\parindent\z@\leavevmode}%
                                {\sphinx@ttlpatch@oktrue}{}%
     \ifsphinx@ttlpatch@ok
     \patchcmd{\ttlh@hang}{\noindent}{}{}{\sphinx@ttlpatch@okfalse}%
     \fi
   }{}%
   \ifsphinx@ttlpatch@ok
     \typeout{^^J Package Sphinx Info: ^^J
              **** titlesec 2.10.1 successfully patched for bugfix ****^^J}%
   \else
     \AtEndDocument{\PackageWarningNoLine{sphinx}{^^J%
******** titlesec 2.10.1 has a bug, (section numbers disappear) ......|^^J%
******** and Sphinx could not patch it, perhaps because your local ...|^^J%
******** copy is already fixed without a changed release date. .......|^^J%
******** If not, you must update titlesec! ...........................|}}%
     \sphinxbuildwarning{badtitlesec}%
   \fi
  }%
 }{}

% Augment the sectioning commands used to get our own font family in place,
% and reset some internal data items (\titleformat from titlesec package)
\titleformat{\section}{\Large\py@HeaderFamily}%
            {\py@TitleColor\thesection}{0.5em}{\py@TitleColor}
\titleformat{\subsection}{\large\py@HeaderFamily}%
            {\py@TitleColor\thesubsection}{0.5em}{\py@TitleColor}
% \normalsize added as work-around to a lualatex-ja upstream problem
% https://osdn.net/projects/luatex-ja/ticket/47321
\titleformat{\subsubsection}{\normalsize\py@HeaderFamily}%
            {\py@TitleColor\thesubsubsection}{0.5em}{\py@TitleColor}
% By default paragraphs (and subsubsections) will not be numbered because
% sphinxmanual.cls and sphinxhowto.cls set secnumdepth to 2
\titleformat{\paragraph}{\normalsize\py@HeaderFamily}%
            {\py@TitleColor\theparagraph}{0.5em}{\py@TitleColor}
\titleformat{\subparagraph}{\normalsize\py@HeaderFamily}%
            {\py@TitleColor\thesubparagraph}{0.5em}{\py@TitleColor}

% Since Sphinx 1.5, users should use HeaderFamily key to 'sphinxsetup' rather
% than defining their own \py@HeaderFamily command (which is still possible).
% Memo: \py@HeaderFamily is also used by \maketitle as defined in
% sphinxmanual.cls/sphinxhowto.cls
\newcommand{\py@HeaderFamily}{\spx@opt@HeaderFamily}

% This sets up the fancy chapter headings that make the documents look
% at least a little better than the usual LaTeX output.
\@ifpackagewith{fncychap}{Bjarne}{
  \ChNameVar {\raggedleft\normalsize \py@HeaderFamily}
  \ChNumVar  {\raggedleft\Large      \py@HeaderFamily}
  \ChTitleVar{\raggedleft\Large      \py@HeaderFamily}
  % This creates (numbered) chapter heads without the leading \vspace*{}:
  \def\@makechapterhead#1{%
    {\parindent \z@ \raggedright \normalfont
      \ifnum \c@secnumdepth >\m@ne
        \if@mainmatter
          \DOCH
        \fi
      \fi
      \interlinepenalty\@M
      \if@mainmatter
        \DOTI{#1}%
      \else%
        \DOTIS{#1}%
      \fi
    }}
}{}% <-- "false" clause of \@ifpackagewith

% fix fncychap's bug which uses prematurely the \textwidth value
\@ifpackagewith{fncychap}{Bjornstrup}
 {\AtBeginDocument{\mylen\textwidth\advance\mylen-2\myhi}}%
 {}% <-- "false" clause of \@ifpackagewith


\endinput
