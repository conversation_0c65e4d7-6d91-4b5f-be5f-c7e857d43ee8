# Translations template for sphinxcontrib-devhelp.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the
# sphinxcontrib-devhelp project.
# <AUTHOR> <EMAIL>, 2019.
# 
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: sphinxcontrib-devhelp 1.0.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2019-01-20 00:43+0900\n"
"PO-Revision-Date: 2019-01-19 15:44+0000\n"
"Language-Team: Turkish (https://www.transifex.com/sphinx-doc/teams/36659/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.6.0\n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: sphinxcontrib/devhelp/__init__.py:52
#, python-format
msgid ""
"To view the help file:\n"
"$ mkdir -p $HOME/.local/share/devhelp/books\n"
"$ ln -s $PWD/%(outdir)s $HOME/.local/share/devhelp/books/%(project)s\n"
"$ devhelp"
msgstr ""

#: sphinxcontrib/devhelp/__init__.py:78
msgid "dumping devhelp index..."
msgstr ""
