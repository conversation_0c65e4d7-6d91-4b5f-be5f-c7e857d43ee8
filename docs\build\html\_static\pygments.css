pre { line-height: 125%; }
td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
.highlight .hll { background-color: #ffffcc }
.highlight { background: #f8f8f8; }
.highlight .c { color: #8F5902; font-style: italic } /* Comment */
.highlight .err { color: #A40000; border: 1px solid #EF2929 } /* Error */
.highlight .g { color: #000 } /* Generic */
.highlight .k { color: #004461; font-weight: bold } /* Keyword */
.highlight .l { color: #000 } /* Literal */
.highlight .n { color: #000 } /* Name */
.highlight .o { color: #582800 } /* Operator */
.highlight .x { color: #000 } /* Other */
.highlight .p { color: #000; font-weight: bold } /* Punctuation */
.highlight .ch { color: #8F5902; font-style: italic } /* Comment.Hashbang */
.highlight .cm { color: #8F5902; font-style: italic } /* Comment.Multiline */
.highlight .cp { color: #8F5902 } /* Comment.Preproc */
.highlight .cpf { color: #8F5902; font-style: italic } /* Comment.PreprocFile */
.highlight .c1 { color: #8F5902; font-style: italic } /* Comment.Single */
.highlight .cs { color: #8F5902; font-style: italic } /* Comment.Special */
.highlight .gd { color: #A40000 } /* Generic.Deleted */
.highlight .ge { color: #000; font-style: italic } /* Generic.Emph */
.highlight .ges { color: #000 } /* Generic.EmphStrong */
.highlight .gr { color: #EF2929 } /* Generic.Error */
.highlight .gh { color: #000080; font-weight: bold } /* Generic.Heading */
.highlight .gi { color: #00A000 } /* Generic.Inserted */
.highlight .go { color: #888 } /* Generic.Output */
.highlight .gp { color: #745334 } /* Generic.Prompt */
.highlight .gs { color: #000; font-weight: bold } /* Generic.Strong */
.highlight .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
.highlight .gt { color: #A40000; font-weight: bold } /* Generic.Traceback */
.highlight .kc { color: #004461; font-weight: bold } /* Keyword.Constant */
.highlight .kd { color: #004461; font-weight: bold } /* Keyword.Declaration */
.highlight .kn { color: #004461; font-weight: bold } /* Keyword.Namespace */
.highlight .kp { color: #004461; font-weight: bold } /* Keyword.Pseudo */
.highlight .kr { color: #004461; font-weight: bold } /* Keyword.Reserved */
.highlight .kt { color: #004461; font-weight: bold } /* Keyword.Type */
.highlight .ld { color: #000 } /* Literal.Date */
.highlight .m { color: #900 } /* Literal.Number */
.highlight .s { color: #4E9A06 } /* Literal.String */
.highlight .na { color: #C4A000 } /* Name.Attribute */
.highlight .nb { color: #004461 } /* Name.Builtin */
.highlight .nc { color: #000 } /* Name.Class */
.highlight .no { color: #000 } /* Name.Constant */
.highlight .nd { color: #888 } /* Name.Decorator */
.highlight .ni { color: #CE5C00 } /* Name.Entity */
.highlight .ne { color: #C00; font-weight: bold } /* Name.Exception */
.highlight .nf { color: #000 } /* Name.Function */
.highlight .nl { color: #F57900 } /* Name.Label */
.highlight .nn { color: #000 } /* Name.Namespace */
.highlight .nx { color: #000 } /* Name.Other */
.highlight .py { color: #000 } /* Name.Property */
.highlight .nt { color: #004461; font-weight: bold } /* Name.Tag */
.highlight .nv { color: #000 } /* Name.Variable */
.highlight .ow { color: #004461; font-weight: bold } /* Operator.Word */
.highlight .pm { color: #000; font-weight: bold } /* Punctuation.Marker */
.highlight .w { color: #F8F8F8 } /* Text.Whitespace */
.highlight .mb { color: #900 } /* Literal.Number.Bin */
.highlight .mf { color: #900 } /* Literal.Number.Float */
.highlight .mh { color: #900 } /* Literal.Number.Hex */
.highlight .mi { color: #900 } /* Literal.Number.Integer */
.highlight .mo { color: #900 } /* Literal.Number.Oct */
.highlight .sa { color: #4E9A06 } /* Literal.String.Affix */
.highlight .sb { color: #4E9A06 } /* Literal.String.Backtick */
.highlight .sc { color: #4E9A06 } /* Literal.String.Char */
.highlight .dl { color: #4E9A06 } /* Literal.String.Delimiter */
.highlight .sd { color: #8F5902; font-style: italic } /* Literal.String.Doc */
.highlight .s2 { color: #4E9A06 } /* Literal.String.Double */
.highlight .se { color: #4E9A06 } /* Literal.String.Escape */
.highlight .sh { color: #4E9A06 } /* Literal.String.Heredoc */
.highlight .si { color: #4E9A06 } /* Literal.String.Interpol */
.highlight .sx { color: #4E9A06 } /* Literal.String.Other */
.highlight .sr { color: #4E9A06 } /* Literal.String.Regex */
.highlight .s1 { color: #4E9A06 } /* Literal.String.Single */
.highlight .ss { color: #4E9A06 } /* Literal.String.Symbol */
.highlight .bp { color: #3465A4 } /* Name.Builtin.Pseudo */
.highlight .fm { color: #000 } /* Name.Function.Magic */
.highlight .vc { color: #000 } /* Name.Variable.Class */
.highlight .vg { color: #000 } /* Name.Variable.Global */
.highlight .vi { color: #000 } /* Name.Variable.Instance */
.highlight .vm { color: #000 } /* Name.Variable.Magic */
.highlight .il { color: #900 } /* Literal.Number.Integer.Long */